package com.tyme.microservices.biovault;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith({InitialExtension.class, SpringExtension.class, MockitoExtension.class})
@Slf4j
public class BioVaultApplicationTests {

	protected final String providerSource = "DUMMY_SOURCE";

	@Test
	void giveValidLocalStack_thenShouldSuccessfullyStart() {
		log.info("verify localStack");
		Assertions.assertTrue(InitialExtension.LOCALSTACK_CONTAINER.isRunning());
	}

	@Test
	void getValidArgs_thenShouldSuccessfullyBioVaultApplicationStart() {
		try (MockedStatic<BioVaultApplication> bioVaultApplicationMockedStatic = Mockito.mockStatic(BioVaultApplication.class)) {
			BioVaultApplication.main(new String[]{});
		}
		Assertions.assertTrue(true);
	}

}



