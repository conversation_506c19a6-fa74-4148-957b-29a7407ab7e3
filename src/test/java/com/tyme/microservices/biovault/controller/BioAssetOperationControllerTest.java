package com.tyme.microservices.biovault.controller;

import com.tyme.microservices.biovault.domain.ResetAssetRequest;
import com.tyme.microservices.biovault.domain.enums.CustomerKeyType;
import com.tyme.microservices.biovault.service.BioAssetOperationService;
import com.tyme.microservices.biovault.service.BioAssetService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BioAssetOperationControllerTest {

    @Mock
    private BioAssetService bioAssetService;

    @Mock
    private BioAssetOperationService bioAssetOperationService;

    private WebTestClient webTestClient;

    @BeforeEach
    void setUp() {
        BioAssetOperationController controller = new BioAssetOperationController(bioAssetService, bioAssetOperationService);
        webTestClient = WebTestClient.bindToController(controller).build();
    }

    @Test
    void getBioResource_Success() {
        // Arrange
        String profileId = "test-profile-id";
        ResetAssetRequest request = ResetAssetRequest.builder()
                .idType(CustomerKeyType.PROFILE_ID)
                .id(profileId)
                .build();

        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("BioAssetInfo", new HashMap<>());
        expectedResponse.put("BioAssetProviderInfo", new HashMap<>());

        when(bioAssetOperationService.getBioResource(any(ResetAssetRequest.class)))
                .thenReturn(Mono.just(expectedResponse));

        // Act & Assert
        webTestClient.post()
                .uri("/bio-vault-operation/get-bio-resource")
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.BioAssetInfo").exists()
                .jsonPath("$.BioAssetProviderInfo").exists();
    }

    @Test
    void getBioResource_InvalidRequest() {
        // Arrange
        ResetAssetRequest request = ResetAssetRequest.builder()
                .build();

        // Act & Assert
        webTestClient.post()
                .uri("/bio-vault-operation/get-bio-resource")
                .bodyValue(request)
                .exchange()
                .expectStatus().isBadRequest();
    }
} 