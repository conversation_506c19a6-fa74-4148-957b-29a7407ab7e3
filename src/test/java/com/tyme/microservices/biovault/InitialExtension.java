package com.tyme.microservices.biovault;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.microservices.biovault.domain.AssetProviderResponseV2;
import com.tyme.microservices.biovault.domain.GetAssetResponse;
import com.tyme.microservices.biovault.domain.MambuIdentity;
import com.tyme.microservices.biovault.domain.MambuProfile;
import com.tyme.microservices.biovault.domain.ProviderDetails;
import com.tyme.microservices.biovault.domain.ResetBaselineResponse;
import com.tyme.microservices.biovault.domain.Resource;
import com.tyme.microservices.biovault.domain.enums.AssetType;
import com.tyme.microservices.biovault.domain.enums.BioSource;
import com.tyme.microservices.biovault.domain.enums.ProviderStatus;
import com.tyme.microservices.biovault.domain.enums.ResourceType;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import okhttp3.mockwebserver.Dispatcher;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import okhttp3.mockwebserver.RecordedRequest;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.extension.BeforeAllCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.testcontainers.containers.localstack.LocalStackContainer;
import org.testcontainers.utility.DockerImageName;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
public class InitialExtension implements BeforeAllCallback, ExtensionContext.Store.CloseableResource {

    private static boolean started = false;

    private static final String PROVIDER_SOURCE = "DUMMY_SOURCE";

    public static LocalStackContainer LOCALSTACK_CONTAINER;

    public static MockWebServer bioAssetProviderMockService;

    public static MockWebServer facialVerificationMockService;

    public static MockWebServer facialRecognitionMockService;

    public static MockWebServer profileMockService;

    private static void startLocalStackContainer() {
        int maxRetry = 3;
        int attempt = 1;
        boolean retry = true;
        do {
            if (!retry) {
                break;
            }
            try {
                Thread.sleep(1000);
                LOCALSTACK_CONTAINER = new LocalStackContainer(DockerImageName.parse("localstack/localstack:2.3.2"))
                        .withServices(LocalStackContainer.Service.S3)
                        .withServices(LocalStackContainer.Service.DYNAMODB);
                LOCALSTACK_CONTAINER.start();
                log.info("LocalStackContainer is running");
                retry = false;
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
            log.info("Retry LocalStackContainer {}", attempt);
        } while (attempt++ <= maxRetry);
    }

    private static void stopLocalStackContainer() {
        if (LOCALSTACK_CONTAINER != null) {
            if (LOCALSTACK_CONTAINER.isRunning()) {
                LOCALSTACK_CONTAINER.stop();
            } else {
                log.error("LocalStackContainer is not running");
            }
        } else {
            log.error("LocalStackContainer is null");
        }
    }

    public static void setCredentialsEnv(LocalStackContainer localstack) {
        if (LOCALSTACK_CONTAINER != null) {
            System.setProperty("cloud.aws.region.static", localstack.getRegion());
            System.setProperty("cloud.aws.credentials.access-key", localstack.getAccessKey());
            System.setProperty("cloud.aws.credentials.secret-key", localstack.getSecretKey());
            System.setProperty("cloud.aws.s3.endpoint", localstack.getEndpointOverride(LocalStackContainer.Service.S3).toString());
            System.setProperty("cloud.aws.dynamodb.endpoint", localstack.getEndpointOverride(LocalStackContainer.Service.DYNAMODB).toString());
        } else {
            System.out.println("localstack container is null");
            System.exit(1);
        }
    }

    public static void setMockWebServerEnv() {
        System.setProperty("app.config.infra.bap.baseUrl", String.format("http://localhost:%s", bioAssetProviderMockService.getPort()));
        System.setProperty("app.config.infra.facial-verification.baseUrl", String.format("http://localhost:%s", facialVerificationMockService.getPort()));
        System.setProperty("app.config.infra.facial-recognition.baseUrl", String.format("http://localhost:%s", facialRecognitionMockService.getPort()));
        System.setProperty("app.config.infra.profile.baseUrl", String.format("http://localhost:%s", profileMockService.getPort()));
        System.setProperty("app.config.photo.expiredTime2D", "1");
    }

    public static void setUpFacialVerificationMockService() {
        log.info("facialVerificationMockService start");
        facialVerificationMockService = new MockWebServer();
        try {
            facialVerificationMockService.setDispatcher(new Dispatcher() {
                @NotNull
                @Override
                public MockResponse dispatch(@NotNull RecordedRequest request) {
                    try {
                        if (Objects.requireNonNull(request.getPath()).contains("/facial-verification-operation/reset")) {
                            return new MockResponse()
                                    .addHeader("Content-Type", "application/json")
                                    .setResponseCode(200)
                                    .setBody(new ObjectMapper().writeValueAsString(
                                            ResetBaselineResponse.builder()
                                                    .status(true)
                                                    .baselines(List.of(""))
                                                    .build()
                                    ));
                        }
                    } catch (Exception ex) {
                        log.error(ex.getMessage(), ex);
                    }
                    return new MockResponse().addHeader("Content-Type", "application/json").setResponseCode(200);
                }
            });
            facialVerificationMockService.start();
        } catch (Exception ex){
            log.error(ex.getMessage(), ex);
        }
    }

    public static void setUpFacialRecognitionMockService() {
        log.info("facialRecognitionMockService start");
        facialRecognitionMockService = new MockWebServer();
        try {
            facialRecognitionMockService.setDispatcher(new Dispatcher() {
                @NotNull
                @Override
                public MockResponse dispatch(@NotNull RecordedRequest request) {
                    try {
                        if (Objects.requireNonNull(request.getPath()).contains("/facial-recognition-operation/reset")) {
                            return new MockResponse()
                                    .addHeader("Content-Type", "application/json")
                                    .setResponseCode(200)
                                    .setBody(new ObjectMapper().writeValueAsString(
                                            Map.of("beforeCount", 0, "afterCount", 0)
                                    ));
                        }
                    } catch (Exception ex) {
                        log.error(ex.getMessage(), ex);
                    }
                    return new MockResponse().addHeader("Content-Type", "application/json").setResponseCode(200);
                }
            });
            facialRecognitionMockService.start();
        } catch (Exception ex){
            log.error(ex.getMessage(), ex);
        }
    }


    public static void setUpProfileMockService() {
        log.info("profileMockService start");

        profileMockService = new MockWebServer();

        try {
            profileMockService.setDispatcher(new Dispatcher() {
                @NotNull
                @Override
                public MockResponse dispatch(@NotNull RecordedRequest request) {
                    try {
                        if (request.getPath() != null &&
                                request.getPath().startsWith("/profile/failbc63-get8-4f29-bd30-profile67c873")) {
                            return new MockResponse().setResponseCode(500);
                        }
                        else if (request.getPath() != null &&
                                request.getPath().startsWith("/profile/emptyc63-get8-4f29-bd30-profile67c873")) {
                            return new MockResponse()
                                    .addHeader("Content-Type", "application/json")
                                    .setResponseCode(200)
                                    .setBody(new org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(
                                            MambuProfile.builder().identities(
                                                    List.of()
                                            ).build()
                                    ));
                        }
                        else if (request.getPath() != null && request.getPath().startsWith("/profile/")) {
                            return new MockResponse()
                                    .addHeader("Content-Type", "application/json")
                                    .setResponseCode(200)
                                    .setBody(new org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(
                                            MambuProfile.builder().identities(
                                                    List.of(
                                                            MambuIdentity.builder().saId("123456").build()
                                                    )).build()
                                    ));
                        }
                    } catch (Exception ex) {
                        log.error(ex.getMessage(), ex);
                    }
                    return new MockResponse().addHeader("Content-Type", "application/json").setResponseCode(200);
                }
            });
            profileMockService.start();
        } catch (Exception ex){
            log.error(ex.getMessage(), ex);
        }
    }


    public static void setUpBioAssetProviderMockWebServer() {
        log.info("bioAssetProviderMockService start");
        bioAssetProviderMockService = new MockWebServer();
        try {
            bioAssetProviderMockService.setDispatcher(new Dispatcher() {
                @NotNull
                @Override
                public MockResponse dispatch(@NotNull RecordedRequest request) {
                    try {
                        if (Objects.requireNonNull(request.getPath()).contains("/v2/bap/assets?type=")) {
                            String requestBodyString = request.getBody().readUtf8();
                            if (requestBodyString.contains("idHasPhoto")) {
                                return new MockResponse()
                                        .addHeader("Content-Type", "application/json")
                                        .setResponseCode(200)
                                        .setBody(new ObjectMapper().writeValueAsString(
                                                List.of(AssetProviderResponseV2.builder()
                                                        .resources(List.of(buildResourceHasPhoto()))
                                                        .build())
                                        ));
                            } else if (requestBodyString.contains("idHasBaseline")) {
                                return new MockResponse()
                                        .addHeader("Content-Type", "application/json")
                                        .setResponseCode(200)
                                        .setBody(new ObjectMapper().writeValueAsString(
                                                List.of(AssetProviderResponseV2.builder()
                                                        .resources(List.of(buildResourceHasBaseline()))
                                                        .build())
                                        ));
                            } else if (requestBodyString.contains("idHasExisting2D")) {
                                return new MockResponse()
                                        .addHeader("Content-Type", "application/json")
                                        .setResponseCode(200)
                                        .setBody(new ObjectMapper().writeValueAsString(
                                                List.of(AssetProviderResponseV2.builder()
                                                        .resources(List.of(buildResourceHasBaseline()))
                                                        .build())
                                        ));
                            } else if (requestBodyString.contains("idHasNoPhoto")) {
                                return new MockResponse()
                                        .addHeader("Content-Type", "application/json")
                                        .setResponseCode(200)
                                        .setBody(new ObjectMapper().writeValueAsString(
                                                List.of(AssetProviderResponseV2.builder()
                                                        .resources(Collections.emptyList())
                                                        .build())
                                        ));
                            } else if (requestBodyString.contains("idServiceUnavailable")) {
                                return new MockResponse()
                                        .addHeader("Content-Type", "application/json")
                                        .setResponseCode(200)
                                        .setBody(new ObjectMapper().writeValueAsString(
                                                List.of(AssetProviderResponseV2.builder()
                                                        .resources(Collections.emptyList())
                                                        .providerDetails(ProviderDetails.builder()
                                                                .code("HANIS")
                                                                .status(ProviderStatus.DOWN).build())
                                                        .build())
                                        ));
                            } else if (requestBodyString.contains("idHasData")) {
                                return new MockResponse()
                                        .addHeader("Content-Type", "application/json")
                                        .setResponseCode(200)
                                        .setBody(new ObjectMapper().writeValueAsString(
                                                List.of(AssetProviderResponseV2.builder()
                                                        .resources(List.of(buildResourceHasData()))
                                                        .build())
                                        ));
                            } else if (requestBodyString.contains("idWithUnsupportedType")) {
                                return new MockResponse()
                                        .addHeader("Content-Type", "application/json")
                                        .setResponseCode(200)
                                        .setBody(new ObjectMapper().writeValueAsString(
                                                List.of(AssetProviderResponseV2.builder()
                                                        .resources(List.of(buildUnsupportedResource()))
                                                        .build())
                                        ));
                            }
                        }
                        if (Objects.requireNonNull(request.getPath()).contains("/bap/assets")) {
                            String requestBodyString = request.getBody().readUtf8();
                            if (requestBodyString.contains("idHasPhoto")) {
                                return new MockResponse()
                                        .addHeader("Content-Type", "application/json")
                                        .setResponseCode(200)
                                        .setBody(new ObjectMapper().writeValueAsString(
                                                GetAssetResponse.builder()
                                                        .id("idHasPhoto")
                                                        .resources(List.of(buildResourceHasPhoto()))
                                                        .build()
                                        ));
                            } else if (requestBodyString.contains("idHasBaseline")) {
                                return new MockResponse()
                                        .addHeader("Content-Type", "application/json")
                                        .setResponseCode(200)
                                        .setBody(new ObjectMapper().writeValueAsString(
                                                GetAssetResponse.builder()
                                                        .id("idHasBaseline")
                                                        .resources(List.of(buildResourceHasBaseline()))
                                                        .build()
                                        ));
                            } else if (requestBodyString.contains("idHasNoPhoto")) {
                                return new MockResponse()
                                        .addHeader("Content-Type", "application/json")
                                        .setResponseCode(200)
                                        .setBody(new ObjectMapper().writeValueAsString(
                                                GetAssetResponse.builder()
                                                        .id("idHasNoPhoto")
                                                        .resources(Collections.emptyList())
                                                        .build()
                                        ));
                            } else if (requestBodyString.contains("idServiceUnavailable")) {
                                return new MockResponse()
                                        .addHeader("Content-Type", "application/json")
                                        .setResponseCode(200)
                                        .setBody(new ObjectMapper().writeValueAsString(
                                                GetAssetResponse.builder()
                                                        .id("idServiceUnavailable")
                                                        .resources(Collections.emptyList())
                                                        .provider(List.of(ProviderDetails.builder()
                                                                .code("HANIS")
                                                                .status(ProviderStatus.DOWN).build()))
                                                        .build()
                                        ));
                            } else if (requestBodyString.contains("idHasData")) {
                                return new MockResponse()
                                        .addHeader("Content-Type", "application/json")
                                        .setResponseCode(200)
                                        .setBody(new ObjectMapper().writeValueAsString(
                                                GetAssetResponse.builder()
                                                        .id("idHasData")
                                                        .resources(List.of(buildResourceHasData()))
                                                        .build()
                                        ));
                            } else if (requestBodyString.contains("idWithUnsupportedType")) {
                                return new MockResponse()
                                        .addHeader("Content-Type", "application/json")
                                        .setResponseCode(200)
                                        .setBody(new ObjectMapper().writeValueAsString(
                                                GetAssetResponse.builder()
                                                        .id("idWithUnsupportedType")
                                                        .resources(List.of(buildUnsupportedResource()))
                                                        .build()
                                        ));
                            }
                        }
                    } catch (Exception ex) {
                        log.error(ex.getMessage(), ex);
                    }
                    return new MockResponse().addHeader("Content-Type", "application/json").setResponseCode(200);
                }
            });
            bioAssetProviderMockService.start();
        } catch (Exception ex){
            log.error(ex.getMessage(), ex);
        }
    }

    public static void tearDownBioAssetProviderMockWebServer() {
        try {
            log.info("tearDownBioAssetProviderMockWebServer");
            bioAssetProviderMockService.shutdown();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    public static void tearDownFacialVerificationMockServer() {
        try {
            log.info("tearDownFacialVerificationMockServer");
            facialVerificationMockService.shutdown();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    public static void tearDownFacialRecognitionMockServer() {
        try {
            log.info("tearDownFacialRecognitionMockServer");
            facialRecognitionMockService.shutdown();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    public static void tearDownProfileMockServiceServer() {
        try {
            log.info("tearDownProfileMockServiceServer");
            profileMockService.shutdown();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    @Override
    public void beforeAll(ExtensionContext context) {
        if (!started) {
            started = true;
            startLocalStackContainer();
            setCredentialsEnv(LOCALSTACK_CONTAINER);
            setUpBioAssetProviderMockWebServer();
            setUpFacialVerificationMockService();
            setUpFacialRecognitionMockService();
            setUpProfileMockService();
            setMockWebServerEnv();
        }
    }

    @Override
    public void close() {
        started = false;
        stopLocalStackContainer();
        tearDownBioAssetProviderMockWebServer();
        tearDownFacialVerificationMockServer();
        tearDownFacialRecognitionMockServer();
        tearDownProfileMockServiceServer();
    }

    private static Resource buildResourceHasPhoto() {
        return Resource.builder()
                .resourceType(ResourceType.RAW_BASE64)
                .type(AssetType.PHOTO_2D)
                .source(PROVIDER_SOURCE)
                .hasValue(true)
                .rawPhoto("cGFzcyB0aGlzIGNhc2U=")
                .build();
    }

    private static Resource buildResourceHasBaseline() {
        return Resource.builder()
                .resourceType(ResourceType.S3_ENDPOINT)
                .type(AssetType.FACE_MAP)
                .source(BioSource.FACETEC_COMPARISON.name())
                .hasValue(true)
                .path("idHasBaseline/selfie.jpg.facemap")
                .build();
    }

    private static Resource buildResourceHasData() {
        return Resource.builder()
                .resourceType(ResourceType.RAW_DATA)
                .type(AssetType.DATA)
                .source(PROVIDER_SOURCE)
                .hasValue(true)
                .rawData("{\"name\":\"JohnDoe\",\"age\":30,\"email\":\"<EMAIL>\"}")
                .rawPhoto("cGFzcyB0aGlzIGNhc2U=")
                .build();
    }

    private static Resource buildUnsupportedResource() {
        return Resource.builder()
                .resourceType(ResourceType.RAW_DATA)
                .type(AssetType.NONE)
                .source(PROVIDER_SOURCE)
                .hasValue(true)
                .rawData("{\"name\":\"JohnDoe\",\"age\":30,\"email\":\"<EMAIL>\"}")
                .rawPhoto("cGFzcyB0aGlzIGNhc2U=")
                .build();
    }
}
