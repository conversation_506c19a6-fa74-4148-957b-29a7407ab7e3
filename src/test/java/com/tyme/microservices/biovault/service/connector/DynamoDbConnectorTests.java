package com.tyme.microservices.biovault.service.connector;

import com.tyme.microservices.biovault.BioVaultApplicationTests;
import com.tyme.microservices.biovault.domain.CustomerKey;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbResource;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbAssetInfo;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbKey;
import com.tyme.microservices.biovault.domain.enums.CustomerKeyType;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.UUID;

@Slf4j
public class DynamoDbConnectorTests extends BioVaultApplicationTests {

    @Autowired
    private DynamoDbConnector dynamoDbConnector;

    @Test
    void givenValidAssetInfo_thenShouldSuccessfullyGetData() {
        String randomProfileId = UUID.randomUUID().toString();
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(randomProfileId)
                .build();

        DynamoDbAssetInfo dynamoDbAssetInfo = DynamoDbAssetInfo.newDynamoDbAssetInfo(customerKey);
        DynamoDbResource dynamoDbResource = DynamoDbResource.builder()
                .id(UUID.randomUUID().toString())
                .path("selfie.json")
                .type("SELFIE")
                .hasValue(true)
                .source("SELFIE").build();
        dynamoDbAssetInfo.setDynamoDbResources(List.of(dynamoDbResource));

        dynamoDbConnector.save(dynamoDbAssetInfo).block();

        StepVerifier.create(dynamoDbConnector
                        .getLatestAssetInfo(DynamoDbKey.toDynamoDbKey(customerKey)))
                .consumeNextWith(assetInfo -> {
                    log.info("assetInfo: {}", assetInfo);
                    Assertions.assertNotNull(assetInfo);
                    Assertions.assertEquals(dynamoDbAssetInfo.getPk(), assetInfo.getPk());
                    Assertions.assertFalse(assetInfo.getDynamoDbResources().isEmpty());
                })
                .verifyComplete();
    }
}
