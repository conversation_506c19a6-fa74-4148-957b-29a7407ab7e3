package com.tyme.microservices.biovault.service;

import com.tyme.microservices.biovault.BioVaultApplicationTests;
import com.tyme.microservices.biovault.config.S3ClientConfig;
import com.tyme.microservices.biovault.domain.AdditionalResource;
import com.tyme.microservices.biovault.domain.ComparisonResult;
import com.tyme.microservices.biovault.domain.CustomerKey;
import com.tyme.microservices.biovault.domain.GetAssetDetails;
import com.tyme.microservices.biovault.domain.GetAssetRequest;
import com.tyme.microservices.biovault.domain.GetAssetResponse;
import com.tyme.microservices.biovault.domain.PrioritySource;
import com.tyme.microservices.biovault.domain.ProviderDetails;
import com.tyme.microservices.biovault.domain.Resource;
import com.tyme.microservices.biovault.domain.UpdateAssetRequest;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbAssetInfo;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbKey;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbResource;
import com.tyme.microservices.biovault.domain.enums.AssetType;
import com.tyme.microservices.biovault.domain.enums.BioSource;
import com.tyme.microservices.biovault.domain.enums.CustomerKeyType;
import com.tyme.microservices.biovault.domain.enums.ProviderStatus;
import com.tyme.microservices.biovault.service.connector.DynamoDbConnector;
import com.tyme.microservices.biovault.service.connector.S3Connector;
import com.tyme.microservices.biovault.service.impl.BioAssetServiceImpl;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Map;
import java.util.function.Consumer;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Date;
import java.util.HexFormat;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
class BioAssetServiceTest extends BioVaultApplicationTests {

    @Autowired
    private BioAssetService bioAssetService;

    @Autowired
    private BioAssetServiceImpl bioAssetServiceImpl;

    @Autowired
    private DynamoDbConnector dynamoDbConnector;

    @Autowired
    private S3Connector s3Connector;

    @Autowired
    private S3ClientConfig s3ClientConfig;



    @Test
    void givenValidRequest_thenShouldGetBioPhotoSuccessfully() {
        String idHasPhoto = "idHasPhoto";
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(idHasPhoto)
                .build();

        GetAssetRequest getAssetRequest = GetAssetRequest.builder()
                .customerKey(customerKey)
                .details(GetAssetDetails.builder()
                        .type(GetAssetDetails.AssetRequestType.PHOTO)
                        .build())
                .build();

        StepVerifier.create(bioAssetService.searchAsset(getAssetRequest))
                .consumeNextWith(assetInfo -> {
                    log.info("assetInfo: {}", assetInfo);
                    Assertions.assertNotNull(assetInfo);
                    Assertions.assertEquals(idHasPhoto, assetInfo.getId());
                    Assertions.assertFalse(assetInfo.getResources().isEmpty());
                })
                .verifyComplete();

    }

    @Test
    void givenNoExistingAsset_thenShouldInitAndGetBioPhotoSuccessfully() {
        String idHasPhoto = "idHasPhoto";
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(idHasPhoto)
                .build();

        GetAssetRequest getAssetRequest = GetAssetRequest.builder()
                .customerKey(customerKey)
                .details(GetAssetDetails.builder()
                        .type(GetAssetDetails.AssetRequestType.PHOTO)
                        .build())
                .build();

        GetAssetResponse getAssetResponse = bioAssetService.searchAsset(getAssetRequest).block();

        Assertions.assertNotNull(getAssetResponse);
        Assertions.assertEquals(idHasPhoto, getAssetResponse.getId());
        Assertions.assertFalse(getAssetResponse.getResources().isEmpty());
    }

    @Test
    void givenNoExistingAsset_thenShouldInitBaselineAndGetBioPhotoSuccessfully() {
        String idHasBaseline = "idHasBaseline";
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(idHasBaseline)
                .build();

        GetAssetRequest getAssetRequest = GetAssetRequest.builder()
                .customerKey(customerKey)
                .details(GetAssetDetails.builder()
                        .type(GetAssetDetails.AssetRequestType.PHOTO)
                        .build())
                .build();

        s3Connector.putObject(s3ClientConfig.getBaselineBucketName(), "idHasBaseline/selfie.jpg.facemap",
                HexFormat.of().parseHex("e04fd020ea3a6910a2d808002b30309d")).block();

        GetAssetResponse getAssetResponse = bioAssetService.searchAsset(getAssetRequest).block();

        Assertions.assertNotNull(getAssetResponse);
        Assertions.assertEquals(idHasBaseline, getAssetResponse.getId());
        Assertions.assertFalse(getAssetResponse.getResources().isEmpty());
        Resource baseLineResource = getAssetResponse.getResources().stream()
                .filter(r -> AssetType.FACE_MAP.equals(r.getType()) && BioSource.FACETEC_COMPARISON.name().equalsIgnoreCase(r.getSource()))
                .findFirst().orElse(null);
        Assertions.assertNotNull(baseLineResource);
        Assertions.assertTrue(baseLineResource.getHasValue());
    }

    @Test
    void givenNoExistingBaseline_thenShouldGetBioPhotoWithNoBaselineSuccessfully() {
        String idHasBaseline = "idHasBaseline";
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(idHasBaseline)
                .build();

        GetAssetRequest getAssetRequest = GetAssetRequest.builder()
                .customerKey(customerKey)
                .details(GetAssetDetails.builder()
                        .type(GetAssetDetails.AssetRequestType.PHOTO)
                        .build())
                .build();

        GetAssetResponse getAssetResponse = bioAssetService.searchAsset(getAssetRequest).block();

        Assertions.assertNotNull(getAssetResponse);
        Assertions.assertEquals(idHasBaseline, getAssetResponse.getId());
        Assertions.assertFalse(getAssetResponse.getResources().isEmpty());
        Resource baseLineResource = getAssetResponse.getResources().stream()
                .filter(r -> AssetType.FACE_MAP.equals(r.getType()) && BioSource.FACETEC_COMPARISON.name().equalsIgnoreCase(r.getSource()))
                .findFirst().orElse(null);
        Assertions.assertNotNull(baseLineResource);
        Assertions.assertFalse(baseLineResource.getHasValue());
    }

    @Test
    void givenUnsupportedAssetType_thenShouldNotHandleSuccessfully() {
        String idWithUnsupportedType = "idWithUnsupportedType";
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(idWithUnsupportedType)
                .build();

        GetAssetRequest getAssetRequest = GetAssetRequest.builder()
                .customerKey(customerKey)
                .details(GetAssetDetails.builder()
                        .type(GetAssetDetails.AssetRequestType.PHOTO)
                        .build())
                .build();

        GetAssetResponse getAssetResponse = bioAssetService.searchAsset(getAssetRequest).block();

        Assertions.assertNotNull(getAssetResponse);
        Assertions.assertEquals(idWithUnsupportedType, getAssetResponse.getId());
        Assertions.assertFalse(getAssetResponse.getResources().isEmpty());
        Assertions.assertFalse(getAssetResponse.getResources().stream().anyMatch(Resource::getHasValue));
    }

    @Test
    void givenExistingResourceProvider_thenShouldInitAllRequiredResourceSuccessfully() {
        String idHasExisting2D = "idHasExisting2D";
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(idHasExisting2D)
                .build();

        DynamoDbAssetInfo dynamoDbAssetInfo = DynamoDbAssetInfo.newDynamoDbAssetInfo(customerKey);
        DynamoDbResource dynamoDbResource2D = DynamoDbResource.builder()
                .id(UUID.randomUUID().toString())
                .path("PHOTO_2D.jpg")
                .type(AssetType.PHOTO_2D.name())
                .hasValue(true)
                .createdDate(new Date())
                .modifiedDate(new Date())
                .source(providerSource).build();
        dynamoDbAssetInfo.setDynamoDbResources(List.of(dynamoDbResource2D));

        dynamoDbConnector.save(dynamoDbAssetInfo).block();

        GetAssetRequest getAssetRequest = GetAssetRequest.builder()
                .customerKey(customerKey)
                .details(GetAssetDetails.builder()
                        .type(GetAssetDetails.AssetRequestType.PHOTO)
                        .build())
                .build();

        GetAssetResponse getAssetResponse = bioAssetService.searchAsset(getAssetRequest).block();

        Assertions.assertNotNull(getAssetResponse);
        Assertions.assertEquals(idHasExisting2D, getAssetResponse.getId());
        Assertions.assertFalse(getAssetResponse.getResources().isEmpty());
        Assertions.assertEquals(5, getAssetResponse.getResources().size());
    }

    @Test
    void givenNoExistingAsset_thenShouldReturnNoPhotoWhenCallingToProvider() {
        String idHasNoPhoto = "idHasNoPhoto";
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(idHasNoPhoto)
                .build();

        GetAssetRequest getAssetRequest = GetAssetRequest.builder()
                .customerKey(customerKey)
                .details(GetAssetDetails.builder()
                        .type(GetAssetDetails.AssetRequestType.PHOTO)
                        .build())
                .build();

        GetAssetResponse getAssetResponse = bioAssetService.searchAsset(getAssetRequest).block();

        Assertions.assertNotNull(getAssetResponse);
        Assertions.assertFalse(getAssetResponse.getResources().isEmpty());
        Assertions.assertTrue(getAssetResponse.getResources().stream().noneMatch(Resource::getHasValue));
    }

    @Test
    void givenNoExistingAsset_thenShouldReturnServiceProviderUnavailableWhenCallingToProvider() {
        String idServiceUnavailable = "idServiceUnavailable";
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(idServiceUnavailable)
                .build();

        GetAssetRequest getAssetRequest = GetAssetRequest.builder()
                .customerKey(customerKey)
                .details(GetAssetDetails.builder()
                        .type(GetAssetDetails.AssetRequestType.PHOTO)
                        .build())
                .build();

        GetAssetResponse getAssetResponse = bioAssetService.searchAsset(getAssetRequest).block();

        Assertions.assertNotNull(getAssetResponse);
        Assertions.assertTrue(getAssetResponse.isProviderDown());

        ProviderDetails provider = getAssetResponse.getProvider().get(0);
        Assertions.assertNotNull(provider);
        Assertions.assertEquals("HANIS", provider.getCode());
        Assertions.assertEquals(ProviderStatus.DOWN, provider.getStatus());
    }

    @Test
    void givenValidRequest_thenShouldUpdateBioPhotoSuccessfully() {
        String randomSaId = UUID.randomUUID().toString();
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(randomSaId)
                .build();

        DynamoDbAssetInfo dynamoDbAssetInfo = DynamoDbAssetInfo.newDynamoDbAssetInfo(customerKey);
        DynamoDbResource dynamoDbResourceSelfie = DynamoDbResource.builder()
                .id(UUID.randomUUID().toString())
                .path("selfie.json")
                .type(AssetType.SELFIE.name())
                .hasValue(false)
                .source(BioSource.CHANNEL.name()).build();
        DynamoDbResource dynamoDbResourceFaceMap = DynamoDbResource.builder()
                .id(UUID.randomUUID().toString())
                .path("selfie.jpg.facemap")
                .type(AssetType.FACE_MAP.name())
                .hasValue(false)
                .source(BioSource.FACETEC_COMPARISON.name()).build();
        dynamoDbAssetInfo.setDynamoDbResources(List.of(dynamoDbResourceSelfie, dynamoDbResourceFaceMap));

        dynamoDbConnector.save(dynamoDbAssetInfo).block();

        UpdateAssetRequest updateAssetRequest = UpdateAssetRequest.builder()
                .customerKey(customerKey)
                .resources(List.of(Resource.builder()
                                .id(dynamoDbResourceSelfie.getId())
                                .type(AssetType.SELFIE)
                                .source(BioSource.CHANNEL.name())
                                .hasValue(true)
                                .path("selfie.json").build(),
                        Resource.builder()
                                .id(dynamoDbResourceFaceMap.getId())
                                .type(AssetType.FACE_MAP)
                                .source(BioSource.FACETEC_COMPARISON.name())
                                .hasValue(true)
                                .path("selfie.jpg.facemap")
                                .comparisonResult(ComparisonResult.builder()
                                        .result(true)
                                        .maxScore(10)
                                        .minimumScore(1)
                                        .score(10)
                                        .comparisonType("COMPARE_3D_TO_3D")
                                        .build())
                                .build()))
                .build();

        StepVerifier.create(bioAssetService.updateAsset(updateAssetRequest))
                .consumeNextWith(response -> {
                    log.info("response: {}", response);
                    Assertions.assertNotNull(response);
                    Assertions.assertEquals(randomSaId, response.getId());
                    Assertions.assertTrue(response.getResult());

                })
                .verifyComplete();

        StepVerifier.create(dynamoDbConnector.getLatestAssetInfo(DynamoDbKey.toDynamoDbKey(customerKey)))
                .consumeNextWith(assetInfo -> {
                    log.info("info: {}", assetInfo);
                    Assertions.assertNotNull(assetInfo);
                    Assertions.assertEquals(randomSaId, assetInfo.getId());
                    Assertions.assertFalse(assetInfo.getDynamoDbResources().isEmpty());

                    DynamoDbResource selfie = assetInfo.getDynamoDbResources().stream()
                            .filter(resource -> AssetType.SELFIE.name().equalsIgnoreCase(resource.getType()))
                            .findFirst().orElse(null);
                    Assertions.assertNotNull(selfie);
                    Assertions.assertTrue(selfie.getHasValue());
                    Assertions.assertEquals("selfie.json", selfie.getPath());

                    DynamoDbResource facemap = assetInfo.getDynamoDbResources().stream()
                            .filter(resource -> AssetType.FACE_MAP.name().equalsIgnoreCase(resource.getType()))
                            .findFirst().orElse(null);
                    Assertions.assertNotNull(facemap);
                    Assertions.assertTrue(facemap.getHasValue());
                    Assertions.assertEquals("selfie.jpg.facemap", facemap.getPath());
                    ComparisonResult comparisonResult = facemap.getComparisonResult();
                    Assertions.assertNotNull(comparisonResult);
                    Assertions.assertTrue(comparisonResult.getResult());
                    Assertions.assertEquals(10, comparisonResult.getMaxScore());
                    Assertions.assertEquals(1, comparisonResult.getMinimumScore());
                    Assertions.assertEquals(10, comparisonResult.getScore());
                    Assertions.assertFalse(comparisonResult.isLiveness());
                    Assertions.assertEquals("COMPARE_3D_TO_3D", comparisonResult.getComparisonType());

                })
                .verifyComplete();

    }

    @Test
    void givenInValidRequest_thenShouldUpdateBioPhotoFailed() {
        String randomProfileId = UUID.randomUUID().toString();
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(randomProfileId)
                .build();

        DynamoDbAssetInfo dynamoDbAssetInfo = DynamoDbAssetInfo.newDynamoDbAssetInfo(customerKey);
        DynamoDbResource dynamoDbResource = DynamoDbResource.builder()
                .id(UUID.randomUUID().toString())
                .path("selfie.json")
                .type(AssetType.SELFIE.name())
                .source(BioSource.CHANNEL.name())
                .hasValue(true).build();
        dynamoDbAssetInfo.setDynamoDbResources(List.of(dynamoDbResource));

        dynamoDbConnector.save(dynamoDbAssetInfo).block();

        UpdateAssetRequest updateAssetRequest = UpdateAssetRequest.builder()
                .customerKey(customerKey)
                .resources(List.of(Resource.builder()
                        .hasValue(true)
                        .type(AssetType.NONE)
                        .path("updated.selfie.json")
                        .build()))
                .build();

        StepVerifier.create(bioAssetService.updateAsset(updateAssetRequest))
                .consumeNextWith(response -> {
                    log.info("response: {}", response);
                    Assertions.assertNotNull(response);
                    Assertions.assertEquals(randomProfileId, response.getId());
                    Assertions.assertFalse(response.getResult());

                })
                .verifyComplete();

        StepVerifier.create(dynamoDbConnector.getLatestAssetInfo(DynamoDbKey.toDynamoDbKey(customerKey)))
                .consumeNextWith(info -> {
                    log.info("info: {}", info);
                    Assertions.assertNotNull(info);
                    Assertions.assertEquals(randomProfileId, info.getId());
                    Assertions.assertEquals("selfie.json", info.getDynamoDbResources().stream()
                            .filter(e -> AssetType.SELFIE.toString().equals(e.getType()))
                            .map(DynamoDbResource::getPath)
                            .findFirst().get());

                })
                .verifyComplete();

    }

    @Test
    void givenExpiredAsset_thenShouldCreateNewOneAndGetBioPhotoSuccessfully() {
        String idHasPhoto = "idHasPhoto";
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(idHasPhoto)
                .build();

        DynamoDbAssetInfo dynamoDbAssetInfo = DynamoDbAssetInfo.newDynamoDbAssetInfo(customerKey);
        DynamoDbResource dynamoDbResource = DynamoDbResource.builder()
                .id(UUID.randomUUID().toString())
                .path("dummy_path")
                .type(AssetType.PHOTO_2D.name())
                .hasValue(true)
                .source(providerSource)
                .createdDate(new Date(System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(999)))
                .modifiedDate(new Date(System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(999)))
                .build();
        DynamoDbResource dynamoDbResourceSelfie = DynamoDbResource.builder()
                .id(UUID.randomUUID().toString())
                .path("dummy_path")
                .type(AssetType.SELFIE.name())
                .hasValue(false)
                .source(BioSource.CHANNEL.name())
                .createdDate(new Date(System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(9999)))
                .modifiedDate(new Date(System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(9999)))
                .build();
        dynamoDbAssetInfo.setDynamoDbResources(List.of(dynamoDbResource, dynamoDbResourceSelfie));
        log.info("init db: {}", dynamoDbAssetInfo);

        dynamoDbConnector.save(dynamoDbAssetInfo).block();

        GetAssetRequest getAssetRequest = GetAssetRequest.builder()
                .customerKey(customerKey)
                .details(GetAssetDetails.builder()
                        .type(GetAssetDetails.AssetRequestType.PHOTO)
                        .build())
                .build();

        GetAssetResponse getAssetResponse = bioAssetService.searchAsset(getAssetRequest).block();
        log.info("bioAssetResponse: {}", getAssetResponse);
        Assertions.assertNotNull(getAssetResponse);
        Assertions.assertEquals(idHasPhoto, getAssetResponse.getId());
        Assertions.assertFalse(getAssetResponse.getResources().isEmpty());

        StepVerifier.create(dynamoDbConnector.getLatestAssetInfo(customerKey.toDynamoDbKey()))
                .consumeNextWith(newSession -> {
                    log.info("newSession: {}", newSession);
                    Assertions.assertNotNull(newSession);
                    Assertions.assertNotEquals(newSession.getSk(), dynamoDbAssetInfo.getSk());
                })
                .verifyComplete();
    }

    @Test
    void givenExpiredAsset_thenShouldCreateNewOneAndCopySuccessfully() {
        String idHasPhoto = "idHasPhoto";
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(idHasPhoto)
                .build();

        DynamoDbAssetInfo dynamoDbAssetInfo = DynamoDbAssetInfo.newDynamoDbAssetInfo(customerKey);
        DynamoDbResource dynamoDbResource = DynamoDbResource.builder()
                .id(UUID.randomUUID().toString())
                .path("dummy_path")
                .type(AssetType.PHOTO_2D.name())
                .hasValue(true)
                .source(providerSource)
                .createdDate(new Date(System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(9999)))
                .modifiedDate(new Date(System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(9999)))
                .build();
        String dummySelfiePath = String.format("BIO_VAULT/PHOTO/%s/%s/selfie.json", dynamoDbAssetInfo.getIdNoise(), dynamoDbAssetInfo.getSk());
        DynamoDbResource dynamoDbResourceSelfie = DynamoDbResource.builder()
                .id(UUID.randomUUID().toString())
                .path(dummySelfiePath)
                .type(AssetType.SELFIE.name())
                .hasValue(true)
                .source(BioSource.CHANNEL.name())
                .createdDate(new Date(System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(9999)))
                .modifiedDate(new Date(System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(9999)))
                .build();
        dynamoDbAssetInfo.setDynamoDbResources(List.of(dynamoDbResource, dynamoDbResourceSelfie));
        log.info("init db: {}", dynamoDbAssetInfo);

        dynamoDbConnector.save(dynamoDbAssetInfo).block();
        s3Connector.putObject(s3ClientConfig.getBucketName(), dummySelfiePath, HexFormat.of().parseHex("e04fd020ea3a6910a2d808002b30309d")).block();

        GetAssetRequest getAssetRequest = GetAssetRequest.builder()
                .customerKey(customerKey)
                .details(GetAssetDetails.builder()
                        .type(GetAssetDetails.AssetRequestType.PHOTO)
                        .build())
                .build();

        GetAssetResponse getAssetResponse = bioAssetService.searchAsset(getAssetRequest).block();
        log.info("bioAssetResponse: {}", getAssetResponse);
        Assertions.assertNotNull(getAssetResponse);
        Assertions.assertEquals(idHasPhoto, getAssetResponse.getId());
        Assertions.assertFalse(getAssetResponse.getResources().isEmpty());

        StepVerifier.create(dynamoDbConnector.getLatestAssetInfo(customerKey.toDynamoDbKey()))
                .consumeNextWith(newSession -> {
                    log.info("newSession: {}", newSession);
                    Assertions.assertNotNull(newSession);
                    Assertions.assertNotEquals(newSession.getSk(), dynamoDbAssetInfo.getSk());

                    String newPath = String.format("BIO_VAULT/PHOTO/%s/%s/selfie.json", newSession.getIdNoise(), newSession.getSk());
                    StepVerifier.create(s3Connector.getObject(s3ClientConfig.getBucketName(), newPath))
                            .consumeNextWith(object -> {
                                log.info("object: {}", object);
                                Assertions.assertNotNull(object);
                            })
                            .verifyComplete();
                })
                .verifyComplete();
    }

    @Test
    void givenValidRequest_thenShouldGetDataSuccessfully() {
        String idHasData = "idHasData";
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(idHasData)
                .build();

        GetAssetRequest getAssetRequest = GetAssetRequest.builder()
                .customerKey(customerKey)
                .details(GetAssetDetails.builder()
                        .type(GetAssetDetails.AssetRequestType.DATA)
                        .isRequiredLatest(false)
                        .prioritySources(List.of(PrioritySource.builder()
                                        .name(providerSource)
                                        .isRequired(true)
                                        .ordinal(1)
                                .build()))
                        .build())
                .build();

        GetAssetResponse getAssetResponse = bioAssetService.searchAsset(getAssetRequest).block();

        Assertions.assertNotNull(getAssetResponse);
        Assertions.assertEquals(idHasData, getAssetResponse.getId());
        Assertions.assertTrue(getAssetResponse.getResources().stream().allMatch(r -> AssetType.DATA.equals(r.getType())));
    }

    @Test
    void givenNoExistingDataResource_thenShouldGetLatestDataSuccessfully() {
        String idHasData = "idHasData";
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(idHasData)
                .build();

        DynamoDbAssetInfo dynamoDbAssetInfo = DynamoDbAssetInfo.newDynamoDbAssetInfo(customerKey);
        log.info("init db: {}", dynamoDbAssetInfo);

        dynamoDbConnector.save(dynamoDbAssetInfo).block();

        GetAssetRequest getAssetRequest = GetAssetRequest.builder()
                .customerKey(customerKey)
                .details(GetAssetDetails.builder()
                        .type(GetAssetDetails.AssetRequestType.DATA)
                        .isRequiredLatest(true)
                        .prioritySources(List.of(PrioritySource.builder()
                                .name(providerSource)
                                .isRequired(true)
                                .ordinal(1)
                                .build()))
                        .build())
                .build();

        GetAssetResponse getAssetResponse = bioAssetService.searchAsset(getAssetRequest).block();

        Assertions.assertNotNull(getAssetResponse);
        Assertions.assertEquals(idHasData, getAssetResponse.getId());
        Assertions.assertTrue(getAssetResponse.getResources().stream().allMatch(r -> AssetType.DATA.equals(r.getType())));
        Resource dataResource = getAssetResponse.getResources().stream().filter(r -> AssetType.DATA.equals(r.getType())).findFirst().get();
        Assertions.assertNotNull(dataResource);
        Assertions.assertNotNull(dataResource.getRawData());
    }

    @Test
    void givenExistingDataResource_thenShouldGetLatestDataAndPhotoSuccessfully() {
        String idHasData = "idHasData";
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(idHasData)
                .build();

        DynamoDbAssetInfo dynamoDbAssetInfo = DynamoDbAssetInfo.newDynamoDbAssetInfo(customerKey);
        DynamoDbResource dynamoDbResource = DynamoDbResource.builder()
                .id(UUID.randomUUID().toString())
                .type(AssetType.DATA.name())
                .hasValue(true)
                .source(providerSource)
                .rawData("{\"name\":\"JohnDoe\"}")
                .createdDate(new Date(System.currentTimeMillis()))
                .modifiedDate(new Date(System.currentTimeMillis() ))
                .build();

        dynamoDbAssetInfo.setDynamoDbResources(List.of(dynamoDbResource));
        log.info("init db: {}", dynamoDbAssetInfo);

        dynamoDbConnector.save(dynamoDbAssetInfo).block();

        GetAssetRequest getAssetRequest = GetAssetRequest.builder()
                .customerKey(customerKey)
                .details(GetAssetDetails.builder()
                        .type(GetAssetDetails.AssetRequestType.DATA)
                        .isRequiredLatest(true)
                        .prioritySources(List.of(PrioritySource.builder()
                                .name(providerSource)
                                .isRequired(true)
                                .ordinal(1)
                                .build()))
                        .build())
                .build();

        String rawData = dynamoDbResource.getRawData();

        GetAssetResponse getAssetResponse = bioAssetService.searchAsset(getAssetRequest).block();

        Assertions.assertNotNull(getAssetResponse);
        Assertions.assertEquals(idHasData, getAssetResponse.getId());
        Assertions.assertTrue(getAssetResponse.getResources().stream().allMatch(r -> AssetType.DATA.equals(r.getType())));
        Resource dataResource = getAssetResponse.getResources().stream().filter(r -> AssetType.DATA.equals(r.getType())).findFirst().get();
        Assertions.assertNotNull(dataResource);
        Assertions.assertNotEquals(dataResource.getRawData(), rawData);
        Assertions.assertNotNull(dataResource.getRawPhoto());

        StepVerifier.create(dynamoDbConnector.getLatestAssetInfo(DynamoDbKey.toDynamoDbKey(customerKey)))
                .consumeNextWith(info -> {
                    log.info("info: {}", info);
                    Assertions.assertNotNull(info);
                    Assertions.assertEquals(idHasData, info.getId());
                    DynamoDbResource resource2D = info.getDynamoDbResources().stream()
                            .filter(e -> AssetType.PHOTO_2D.name().equals(e.getType()))
                            .findFirst().orElse(null);
                    Assertions.assertNotNull(resource2D);
                    Assertions.assertTrue(resource2D.getHasValue());

                    StepVerifier
                            .create(s3Connector.getObject(s3ClientConfig.getBucketName(), resource2D.getPath()))
                            .consumeNextWith(Assertions::assertNotNull)
                            .verifyComplete();
                })
                .verifyComplete();

    }

    @Test
    void givenValidDynamoDbResources_thenShouldSuccessfullyConcludeAdditionalResources1() {
        DynamoDbAssetInfo dynamoDbAssetInfo = DynamoDbAssetInfo.builder().dynamoDbResources(new ArrayList<>()).build();
        Mono<DynamoDbAssetInfo> dynamoDbAssetInfoMono = bioAssetServiceImpl.concludeAdditionalResources(dynamoDbAssetInfo, Map.of(AssetType.PHOTO_2D.name(),
                AdditionalResource.builder().rawPhoto(Base64.getEncoder().encodeToString("test".getBytes())).type(AssetType.PHOTO_2D).build()));
        StepVerifier
                .create(dynamoDbAssetInfoMono)
                .consumeNextWith(Assertions::assertNotNull)
                .verifyComplete();
    }

    @Test
    void givenValidDynamoDbResources_thenShouldSuccessfullyConcludeAdditionalResources2() {
        DynamoDbAssetInfo dynamoDbAssetInfo = DynamoDbAssetInfo.builder().dynamoDbResources(new ArrayList<>()).build();
        dynamoDbAssetInfo.getDynamoDbResources().add(DynamoDbResource.builder().type(AssetType.PHOTO_2D.name()).build());
        Mono<DynamoDbAssetInfo> dynamoDbAssetInfoMono = bioAssetServiceImpl.concludeAdditionalResources(dynamoDbAssetInfo, Map.of(AssetType.PHOTO_2D.name(),
                AdditionalResource.builder().rawPhoto(Base64.getEncoder().encodeToString("test".getBytes())).type(AssetType.PHOTO_2D).build()));
        StepVerifier
                .create(dynamoDbAssetInfoMono)
                .consumeNextWith(Assertions::assertNotNull)
                .verifyComplete();
    }

    @Test
    void givenInvalidDynamoDbResources_thenShouldFailConcludeAdditionalResources3() {
        DynamoDbAssetInfo dynamoDbAssetInfo = DynamoDbAssetInfo.builder().dynamoDbResources(new ArrayList<>()).build();
        dynamoDbAssetInfo.getDynamoDbResources().add(DynamoDbResource.builder().type(AssetType.NONE.name()).build());
        Mono<DynamoDbAssetInfo> dynamoDbAssetInfoMono = bioAssetServiceImpl.concludeAdditionalResources(dynamoDbAssetInfo, Map.of(AssetType.PHOTO_2D.name(),
                AdditionalResource.builder().type(AssetType.PHOTO_2D).build()));
        StepVerifier
                .create(dynamoDbAssetInfoMono)
                .consumeNextWith(Assertions::assertNotNull)
                .verifyComplete();
    }

    @Test
    void givenValidExpiredTime_thenShouldSuccessfullyResourceExpired() {
        boolean resourceExpired = bioAssetServiceImpl.isResourceExpired(DynamoDbResource.builder().build(), 0);
        Assertions.assertFalse(resourceExpired);
    }

    @Test
    void givenValidModifiedDate_thenShouldSuccessfullyResourceExpired() {
        boolean resourceExpired = bioAssetServiceImpl.isResourceExpired(DynamoDbResource.builder().build(), 100);
        Assertions.assertTrue(resourceExpired);
    }

}
