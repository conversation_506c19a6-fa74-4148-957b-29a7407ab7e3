package com.tyme.microservices.biovault.service.controller;

import com.tyme.microservices.biovault.BioVaultApplicationTests;
import com.tyme.microservices.biovault.controller.BioAssetController;
import com.tyme.microservices.biovault.domain.GetAssetDetails;
import com.tyme.microservices.biovault.domain.GetAssetRequest;
import com.tyme.microservices.biovault.domain.CustomerKey;
import com.tyme.microservices.biovault.domain.GetAssetResponse;
import com.tyme.microservices.biovault.domain.Resource;
import com.tyme.microservices.biovault.domain.UpdateAssetRequest;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbAssetInfo;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbKey;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbResource;
import com.tyme.microservices.biovault.domain.enums.AssetType;
import com.tyme.microservices.biovault.domain.enums.BioSource;
import com.tyme.microservices.biovault.domain.enums.CustomerKeyType;
import com.tyme.microservices.biovault.service.connector.DynamoDbConnector;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.UUID;

@Slf4j
public class BioAssetControllerTest extends BioVaultApplicationTests {

    @Autowired
    private BioAssetController bioAssetController;

    @Autowired
    private DynamoDbConnector dynamoDbConnector;

    @Test
    void givenEmptyTypeRequest_thenShouldGetBioAssetSuccessfully() {
        String idHasBaseline = "idHasBaseline";
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(idHasBaseline)
                .build();

        DynamoDbAssetInfo dynamoDbAssetInfo = DynamoDbAssetInfo.newDynamoDbAssetInfo(customerKey);
        DynamoDbResource dynamoDbResource = DynamoDbResource.builder()
                .id(UUID.randomUUID().toString())
                .path("selfie.json")
                .type(AssetType.SELFIE.name())
                .hasValue(true)
                .source(BioSource.CHANNEL.name()).build();
        dynamoDbAssetInfo.setDynamoDbResources(List.of(dynamoDbResource));

        dynamoDbConnector.save(dynamoDbAssetInfo).block();

        GetAssetRequest getAssetRequest = GetAssetRequest.builder()
                .customerKey(customerKey)
                .details(GetAssetDetails.builder()
                        .type(GetAssetDetails.AssetRequestType.PHOTO)
                        .build())
                .build();

        GetAssetResponse getAssetResponse = bioAssetController.searchAsset(getAssetRequest).block();
        log.info("getAssetResponse: {}", getAssetResponse);
        Assertions.assertNotNull(getAssetResponse);
        Assertions.assertEquals(idHasBaseline, getAssetResponse.getId());
        Assertions.assertFalse(getAssetResponse.getResources().isEmpty());

    }

    @Test
    void givenValidRequest_thenShouldUpdateBioPhotoSuccessfully() {
        String randomProfileId = UUID.randomUUID().toString();
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(randomProfileId)
                .build();

        DynamoDbAssetInfo dynamoDbAssetInfo = DynamoDbAssetInfo.newDynamoDbAssetInfo(customerKey);
        DynamoDbResource dynamoDbResource = DynamoDbResource.builder()
                .id(UUID.randomUUID().toString())
                .path("selfie.json")
                .type("SELFIE")
                .source("SELFIE").build();
        dynamoDbAssetInfo.setDynamoDbResources(List.of(dynamoDbResource));

        dynamoDbConnector.save(dynamoDbAssetInfo).block();

        UpdateAssetRequest updateAssetRequest = UpdateAssetRequest.builder()
                .customerKey(customerKey)
                .resources(List.of(Resource.builder()
                        .id(dynamoDbResource.getId())
                        .hasValue(true)
                        .source(BioSource.CHANNEL.name())
                        .type(AssetType.SELFIE)
                        .path("selfie.json")
                        .build()))
                .build();

        StepVerifier.create(bioAssetController.updateAsset(updateAssetRequest))
                .consumeNextWith(response -> {
                    log.info("response: {}", response);
                    Assertions.assertNotNull(response);
                    Assertions.assertEquals(randomProfileId, response.getId());
                    Assertions.assertTrue(response.getResult());

                })
                .verifyComplete();

        StepVerifier.create(dynamoDbConnector.getLatestAssetInfo(DynamoDbKey.toDynamoDbKey(customerKey)))
                .consumeNextWith(assetInfo -> {
                    log.info("info: {}", assetInfo);
                    Assertions.assertNotNull(assetInfo);
                    Assertions.assertEquals(randomProfileId, assetInfo.getId());

                    Assertions.assertEquals("selfie.json", assetInfo.getDynamoDbResources().stream()
                            .filter(e -> AssetType.SELFIE.toString().equals(e.getType()))
                            .map(DynamoDbResource::getPath)
                            .findFirst().get());

                })
                .verifyComplete();

    }
}
