package com.tyme.microservices.biovault.service.controller;

import com.tyme.microservices.biovault.BioVaultApplicationTests;
import com.tyme.microservices.biovault.controller.BioAssetOperationController;
import com.tyme.microservices.biovault.domain.CustomerKey;
import com.tyme.microservices.biovault.domain.ResetAssetRequest;
import com.tyme.microservices.biovault.domain.ResetAssetResponse;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbAssetInfo;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbKey;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbResource;
import com.tyme.microservices.biovault.domain.enums.AssetType;
import com.tyme.microservices.biovault.domain.enums.CustomerKeyType;
import com.tyme.microservices.biovault.domain.enums.ResetDataType;
import com.tyme.microservices.biovault.service.connector.DynamoDbConnector;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@Slf4j
public class BioAssetOperationControllerTest extends BioVaultApplicationTests {

    @Autowired
    BioAssetOperationController bioAssetOperationController;

    @Autowired
    DynamoDbConnector dynamoDbConnector;

    @Test
    void givenSaIdRequest_thenShouldResetAssetSuccessfully() {

        String randomSaId = UUID.randomUUID().toString();
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.SA_ID)
                .value(randomSaId)
                .build();
        DynamoDbAssetInfo dynamoDbAssetInfo = DynamoDbAssetInfo.newDynamoDbAssetInfo(customerKey);
        DynamoDbResource dynamoDbResource = DynamoDbResource.builder()
                .id(UUID.randomUUID().toString())
                .path("selfie.json")
                .type("SELFIE")
                .source("SELFIE").build();
        dynamoDbAssetInfo.setDynamoDbResources(List.of(dynamoDbResource));

        dynamoDbConnector.save(dynamoDbAssetInfo).block();

        Mono<ResetAssetResponse> resetAssetResponseMono =
                bioAssetOperationController.resetAsset(ResetAssetRequest.builder().id(randomSaId).dataType(ResetDataType.ALL).idType(CustomerKeyType.SA_ID).build());
        StepVerifier.create(resetAssetResponseMono)
                .consumeNextWith(resetAssetResponse -> {
                    Assertions.assertTrue(resetAssetResponse.isStatus());
                })
                .verifyComplete();
    }

    @Test
    void givenProfileIdRequest_thenShouldResetAssetSuccessfully() {

        String randomProfileId = "123456";
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(CustomerKeyType.PROFILE_ID)
                .value(randomProfileId)
                .build();

        DynamoDbAssetInfo dynamoDbAssetInfo = DynamoDbAssetInfo.newDynamoDbAssetInfo(customerKey);
        DynamoDbResource dynamoDbResource = DynamoDbResource.builder()
                .id(UUID.randomUUID().toString())
                .path("selfie.json")
                .type("SELFIE")
                .source("SELFIE").build();
        dynamoDbAssetInfo.setDynamoDbResources(List.of(dynamoDbResource));

        dynamoDbConnector.save(dynamoDbAssetInfo).block();

        Mono<ResetAssetResponse> resetResponseMono =
                bioAssetOperationController.resetAsset(ResetAssetRequest.builder().id(randomProfileId).dataType(ResetDataType.ALL).idType(CustomerKeyType.PROFILE_ID).build());
        StepVerifier.create(resetResponseMono)
                .consumeNextWith(resetAssetResponse -> {
                    Assertions.assertTrue(resetAssetResponse.isStatus());
                })
                .verifyComplete();
    }

}
