package com.tyme.microservices.biovault.service.connector;

import com.tyme.microservices.biovault.domain.AssetProviderOperationRequest;
import com.tyme.microservices.biovault.domain.AssetProviderOperationResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriBuilder;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.net.URI;
import java.util.function.Function;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

@ExtendWith(MockitoExtension.class)
class BioAssetProviderConnectorTest {

    @Mock
    private WebClient.Builder webClientBuilder;

    @Mock
    private WebClient webClient;

    private BioAssetProviderConnector bioAssetProviderConnector;

    @BeforeEach
    void setUp() {
        doReturn(webClient).when(webClientBuilder).build();
        bioAssetProviderConnector = new BioAssetProviderConnector(webClientBuilder.build());
        ReflectionTestUtils.setField(bioAssetProviderConnector, "customerAssetsPath", "/bap/assets");
    }

    @Test
    void getOperationAssetFromProvider_Success() {
        // Arrange
        AssetProviderOperationRequest request = AssetProviderOperationRequest.builder()
                .profileId("test-profile-id")
                .build();

        AssetProviderOperationResponse expectedResponse = AssetProviderOperationResponse.builder()
                .externalId("test-external-id")
                .build();

        WebClient.RequestBodyUriSpec requestBodyUriSpec = mock(WebClient.RequestBodyUriSpec.class);
        WebClient.RequestBodySpec requestBodySpec = mock(WebClient.RequestBodySpec.class);
        WebClient.ResponseSpec responseSpec = mock(WebClient.ResponseSpec.class);

        doReturn(requestBodyUriSpec).when(webClient).post();
        doReturn(requestBodySpec).when(requestBodyUriSpec).uri(any(Function.class));
        doReturn(requestBodySpec).when(requestBodySpec).bodyValue(any());
        doReturn(responseSpec).when(requestBodySpec).retrieve();
        doReturn(Mono.just(expectedResponse)).when(responseSpec).bodyToMono(AssetProviderOperationResponse.class);

        // Act & Assert
        StepVerifier.create(bioAssetProviderConnector.getOperationAssetFromProvider(request))
                .expectNext(expectedResponse)
                .verifyComplete();
    }

    @Test
    void getOperationAssetFromProvider_Error() {
        // Arrange
        AssetProviderOperationRequest request = AssetProviderOperationRequest.builder()
                .profileId("test-profile-id")
                .build();

        WebClient.RequestBodyUriSpec requestBodyUriSpec = mock(WebClient.RequestBodyUriSpec.class);
        WebClient.RequestBodySpec requestBodySpec = mock(WebClient.RequestBodySpec.class);
        WebClient.ResponseSpec responseSpec = mock(WebClient.ResponseSpec.class);

        doReturn(requestBodyUriSpec).when(webClient).post();
        doReturn(requestBodySpec).when(requestBodyUriSpec).uri(any(Function.class));
        doReturn(requestBodySpec).when(requestBodySpec).bodyValue(any());
        doReturn(responseSpec).when(requestBodySpec).retrieve();
        doReturn(Mono.error(new RuntimeException("Test error"))).when(responseSpec).bodyToMono(AssetProviderOperationResponse.class);

        // Act & Assert
        StepVerifier.create(bioAssetProviderConnector.getOperationAssetFromProvider(request))
                .expectError(RuntimeException.class)
                .verify();
    }
} 