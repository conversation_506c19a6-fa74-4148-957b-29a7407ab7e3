package com.tyme.microservices.biovault.service.storage;

import com.tyme.microservices.biovault.BioVaultApplicationTests;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;

import java.util.HexFormat;

@Slf4j
class S3FaceStorageTest extends BioVaultApplicationTests {

    @Autowired
    private S3FaceStorage s3FaceStorage;

    @Test
    void shouldUploadPhotoSuccessfully() {
        String path = "profileId123";
        byte[] data = HexFormat.of().parseHex("e04fd020ea3a6910a2d808002b30309d");

        Mono<PutObjectResponse> putObjectResponseMono = s3FaceStorage.uploadPhoto(path, data);

        StepVerifier
                .create(putObjectResponseMono)
                .consumeNextWith(Assertions::assertNotNull)
                .verifyComplete();
    }
}
