package com.tyme.microservices.biovault.service.impl;

import com.tyme.microservices.biovault.domain.AssetProviderOperationRequest;
import com.tyme.microservices.biovault.domain.AssetProviderOperationResponse;
import com.tyme.microservices.biovault.domain.CustomerKey;
import com.tyme.microservices.biovault.domain.ResetAssetRequest;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbAssetInfo;
import com.tyme.microservices.biovault.domain.enums.CustomerKeyType;
import com.tyme.microservices.biovault.service.connector.BioAssetProviderConnector;
import com.tyme.microservices.biovault.service.connector.DynamoDbConnector;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BioAssetOperationServiceImplTest {

    @Mock
    private DynamoDbConnector dynamoDbConnector;

    @Mock
    private BioAssetProviderConnector bioAssetProviderConnector;

    private BioAssetOperationServiceImpl bioAssetOperationService;

    @BeforeEach
    void setUp() {
        bioAssetOperationService = new BioAssetOperationServiceImpl(dynamoDbConnector, bioAssetProviderConnector);
    }

    @Test
    void getBioResource_WithProfileId_Success() {
        // Arrange
        String profileId = "test-profile-id";
        ResetAssetRequest request = ResetAssetRequest.builder()
                .idType(CustomerKeyType.PROFILE_ID)
                .id(profileId)
                .build();

        DynamoDbAssetInfo dynamoDbAssetInfo = DynamoDbAssetInfo.builder()
                .id(profileId)
                .build();

        AssetProviderOperationResponse providerResponse = AssetProviderOperationResponse.builder()
                .externalId("test-external-id")
                .build();

        when(dynamoDbConnector.getLatestAssetInfo(any())).thenReturn(Mono.just(dynamoDbAssetInfo));
        when(bioAssetProviderConnector.getOperationAssetFromProvider(any(AssetProviderOperationRequest.class)))
                .thenReturn(Mono.just(providerResponse));

        // Act & Assert
        StepVerifier.create(bioAssetOperationService.getBioResource(request))
                .expectNextMatches(response -> {
                    Map<String, Object> result = (Map<String, Object>) response;
                    return result.containsKey("BioAssetInfo") &&
                            result.containsKey("BioAssetProviderInfo") &&
                            result.get("BioAssetInfo") instanceof DynamoDbAssetInfo &&
                            result.get("BioAssetProviderInfo") instanceof AssetProviderOperationResponse;
                })
                .verifyComplete();
    }

    @Test
    void getBioResource_WithNonProfileId_Success() {
        // Arrange
        String idNumber = "123456789";
        ResetAssetRequest request = ResetAssetRequest.builder()
                .idType(CustomerKeyType.IDENTITY_NUMBER)
                .id(idNumber)
                .build();

        DynamoDbAssetInfo dynamoDbAssetInfo = DynamoDbAssetInfo.builder()
                .id(idNumber)
                .build();

        when(dynamoDbConnector.getLatestAssetInfo(any())).thenReturn(Mono.just(dynamoDbAssetInfo));

        // Act & Assert
        StepVerifier.create(bioAssetOperationService.getBioResource(request))
                .expectNextMatches(response -> {
                    Map<String, Object> result = (Map<String, Object>) response;
                    return result.containsKey("BioAssetInfo") &&
                            result.containsKey("BioAssetProviderInfo") &&
                            result.get("BioAssetInfo") instanceof DynamoDbAssetInfo &&
                            result.get("BioAssetProviderInfo") instanceof AssetProviderOperationResponse;
                })
                .verifyComplete();
    }

    @Test
    void getBioResource_NoAssetInfo_Success() {
        // Arrange
        String profileId = "test-profile-id";
        ResetAssetRequest request = ResetAssetRequest.builder()
                .idType(CustomerKeyType.PROFILE_ID)
                .id(profileId)
                .build();

        when(dynamoDbConnector.getLatestAssetInfo(any())).thenReturn(Mono.empty());
        when(bioAssetProviderConnector.getOperationAssetFromProvider(any(AssetProviderOperationRequest.class)))
                .thenReturn(Mono.just(AssetProviderOperationResponse.builder().build()));

        // Act & Assert
        StepVerifier.create(bioAssetOperationService.getBioResource(request))
                .expectNextMatches(response -> {
                    Map<String, Object> result = (Map<String, Object>) response;
                    return result.containsKey("BioAssetInfo") &&
                            result.containsKey("BioAssetProviderInfo") &&
                            result.get("BioAssetInfo") instanceof DynamoDbAssetInfo &&
                            result.get("BioAssetProviderInfo") instanceof AssetProviderOperationResponse;
                })
                .verifyComplete();
    }
} 