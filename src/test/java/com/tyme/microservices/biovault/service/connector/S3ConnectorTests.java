package com.tyme.microservices.biovault.service.connector;

import com.tyme.microservices.biovault.BioVaultApplicationTests;
import com.tyme.microservices.biovault.config.S3ClientConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.test.StepVerifier;
import software.amazon.awssdk.services.s3.S3AsyncClient;

import java.util.HexFormat;

@Slf4j
public class S3ConnectorTests extends BioVaultApplicationTests {

    @Autowired
    public S3ClientConfig s3ClientConfig;

    @Autowired
    public S3Connector s3Connector;

    @Autowired
    public S3AsyncClient s3AsyncClient;

    @Test
    void givenValidPath_thenShouldSuccessfullyUpload() {
        String path = "profileId123";
        byte[] data = HexFormat.of().parseHex("e04fd020ea3a6910a2d808002b30309d");

        StepVerifier
                .create(s3Connector.putObject(s3ClientConfig.getBucketName(), path, data))
                .consumeNextWith(response -> {

                    Assertions.assertNotNull(response);

                    StepVerifier
                            .create(s3Connector.getObject(s3ClientConfig.getBucketName(), path))
                            .consumeNextWith(content -> Assertions.assertArrayEquals(data, content))
                            .verifyComplete();
                })
                .verifyComplete();


    }
}
