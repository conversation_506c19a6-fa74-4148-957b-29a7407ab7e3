package com.tyme.microservices.biovault.service.impl;

import com.tyme.microservices.biovault.domain.CustomerKey;
import com.tyme.microservices.biovault.domain.enums.CustomerKeyType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BAPIntegrationSAServiceImplTest {

  @InjectMocks
  private BAPIntegrationSAServiceImpl bapIntegrationSAService;

  @Test
  void buildFaceMapFallbackRequest() {
    var result = bapIntegrationSAService.buildFaceMapFallbackRequest(CustomerKey.builder()
        .keyType(CustomerKeyType.PROFILE_ID)
        .value("profile_id")
        .build());
    Assertions.assertNotNull(result);
  }
}