package com.tyme.microservices.biovault.service.controller;

import com.tyme.microservices.biovault.controller.validator.CustomerKeyValidator;
import com.tyme.microservices.biovault.domain.CustomerKey;
import com.tyme.microservices.biovault.domain.enums.CustomerKeyType;
import jakarta.validation.ConstraintValidatorContext;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
@Slf4j
public class CustomerKeyValidatorTest {

    private final String VALID_SAID = "7911110766186";

    private final String VALID_PROFILE_ID = "8a9296908e17fd7c018e184832cd0698";

    private static CustomerKeyValidator customerKeyValidator;

    @Mock
    private ConstraintValidatorContext context;

    @BeforeAll
    public static void setup(){
        log.info("setup customerKeyValidator");
        customerKeyValidator = new CustomerKeyValidator();
    }

    @Test
    void givenValidSaId_whenCallIsValid_thenReturnTrue() {
        //when
        boolean isValid = customerKeyValidator.isValid(CustomerKey.builder().keyType(CustomerKeyType.SA_ID)
                .value(VALID_SAID).build(), context);

        //then
        assertTrue(isValid);
    }

    @Test
    void givenEmptySaID_whenCallIsValid_thenReturnFalse(){
        //given
        final String SAID = "";

        //when
        boolean isValid = customerKeyValidator.isValid(CustomerKey.builder().keyType(CustomerKeyType.SA_ID)
                .value(SAID).build(), context);

        //then
        assertFalse(isValid);
    }

    @Test
    void given14CharsSaId_whenCallIsValid_thenReturnFalse(){
        //given
        final String SAID = "12345678901234";

        //when
        boolean isValid = customerKeyValidator.isValid(CustomerKey.builder().keyType(CustomerKeyType.SA_ID)
                .value(SAID).build(), context);

        //then
        assertFalse(isValid);
    }

    @Test
    void given11stCharInvalidSaId_whenCallIsValid_thenReturnFalse(){
        //given
        final StringBuilder builder = new StringBuilder(VALID_SAID);
        builder.setCharAt(10, '4');
        final String SAID = builder.toString();

        //when
        boolean isValid = customerKeyValidator.isValid(CustomerKey.builder().keyType(CustomerKeyType.SA_ID)
                .value(SAID).build(), context);

        //then
        assertFalse(isValid);
    }

    @Test
    void givenInvalidLuhnChecksumSaId_whenCallIsValid_thenReturnFalse(){
        //given
        final StringBuilder builder = new StringBuilder(VALID_SAID);
        builder.setCharAt(12, '9'); //with valid saId as 7911110766186, we choose 9 as invalid checksum
        final String SAID = builder.toString();

        //when
        boolean isValid = customerKeyValidator.isValid(CustomerKey.builder().keyType(CustomerKeyType.SA_ID)
                .value(SAID).build(), context);

        //then
        assertFalse(isValid);
    }


    @Test
    void givenValidProfileId_whenCallIsValid_thenReturnTrue() {
        //when
        boolean isValid = customerKeyValidator.isValid(CustomerKey.builder().keyType(CustomerKeyType.PROFILE_ID)
                .value(VALID_PROFILE_ID).build(), context);

        //then
        assertTrue(isValid);
    }

    @Test
    void givenInValidProfileId_whenCallIsValid_thenReturnFalse() {
        //when
        boolean isValid = customerKeyValidator.isValid(CustomerKey.builder().keyType(CustomerKeyType.PROFILE_ID)
                .value("").build(), context);

        //then
        assertFalse(isValid);
    }

    @Test
    void givenInValidProfileIdWithSpecialChar_whenCallIsValid_thenReturnFalse() {
        //when
        boolean isValid = customerKeyValidator.isValid(CustomerKey.builder().keyType(CustomerKeyType.PROFILE_ID)
                .value("123!!").build(), context);

        //then
        assertFalse(isValid);
    }

    @Test
    void givenInValidProfileIdWithExceedLength_whenCallIsValid_thenReturnFalse() {
        //when
        boolean isValid = customerKeyValidator.isValid(CustomerKey.builder().keyType(CustomerKeyType.PROFILE_ID)
                .value(VALID_PROFILE_ID + "1").build(), context);

        //then
        assertFalse(isValid);
    }

    @Test
    void givenUnsupportedType_whenCallIsValid_thenReturnFalse() {
        //when
        boolean isValid = customerKeyValidator.isValid(CustomerKey.builder().keyType(CustomerKeyType.DRIVER_LICENSE_ID)
                .value("dummy").build(), context);

        //then
        assertFalse(isValid);
    }
}
