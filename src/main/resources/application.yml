spring:
    application:
        name: bio-vault-svc
    jackson:
        serialization:
            write-dates-as-timestamps: false
    autoconfigure.exclude:
        - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
        - org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration
    profiles:
        active: "${SPRING_PROFILES_ACTIVE:local}"
        include: "${ACTIVE_COUNTRY}"

server:
    port: 8138

app:
    endpoint:
        prefix.bio-asset: "/internal/bio-vault"
    config:
        infra:
            bap:
                baseUrl: ${BIO_ASSET_PROVIDER_URL}
                customer-assets-path: ${CUSTOMER_ASSETS_PATH}
                timeOut: 65000
            facial-verification:
                baseUrl: ${FACIAL_VERIFICATION_URL:}
                timeOut: 60000
            facial-recognition:
                baseUrl: ${FACIAL_RECOGNITION_URL:}
                timeOut: 60000
            profile:
                baseUrl: ${PROFILE_URL:}
                timeOut: 60000
        photo:
            expiredTime2D: ${PHOTO-EXPIRED-TIME-2D:0} # minutes unit

logging.level:
    com.tyme:
        common.arcee: ERROR
    org:
        apache:
            kafka:
                client: ERROR
                common: ERROR

management:
  endpoints:
    enabled-by-default: false
  endpoint:
    health:
      enabled: true
      show-details: "always"
