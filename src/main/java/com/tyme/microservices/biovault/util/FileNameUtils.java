package com.tyme.microservices.biovault.util;

import com.tyme.microservices.biovault.domain.enums.AssetType;
import com.tyme.microservices.biovault.domain.enums.BioSource;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class FileNameUtils {

    private FileNameUtils() {

    }

    public static String getFileName(AssetType assetType, String bioSource) {
        switch (assetType) {
            case PHOTO_2D -> {
                return "PHOTO_2D.jpg";
            }
            case SELFIE -> {
                if (BioSource.CHANNEL.name().equalsIgnoreCase(bioSource)) {
                    return "selfie.json";
                } else {
                    return "selfie.jpg";
                }
            }
            case FACE_MAP -> {
                if (BioSource.FACETEC_COMPARISON.name().equalsIgnoreCase(bioSource)) {
                    return "selfie.jpg.facemap";
                } else if (BioSource.FACETEC_LIVENESS_CHECK.name().equalsIgnoreCase(bioSource)) {
                    return "liveness.selfie.jpg.facemap";
                }

                return "selfie.jpg.facemap";
            }
            default -> log.warn("Unsupported type: {}", assetType);
        }

        return null;
    }

    /**
     * get baseline file name from provider
     * @param path the path {uuid}/{filename}
     * @return file name
     */
    public static String getBaselineFileName(String path) {
        return path.substring(path.lastIndexOf('/') + 1);
    }
}
