package com.tyme.microservices.biovault.domain.dynamodb;

import com.fasterxml.uuid.Generators;
import com.tyme.microservices.biovault.config.md5.MD5Util;
import com.tyme.microservices.biovault.domain.CustomerKey;
import com.tyme.microservices.biovault.domain.dynamodb.converter.InstantAsEpochMilliAttributeConverter;
import com.tyme.microservices.biovault.domain.dynamodb.converter.ResourceListConverter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.extensions.annotations.DynamoDbAutoGeneratedTimestampAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.UpdateBehavior;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbUpdateBehavior;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Data
@DynamoDbBean
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DynamoDbAssetInfo {

    @Getter(onMethod = @__({
            @DynamoDbAttribute("pk"),
            @DynamoDbPartitionKey
    }))
    private String pk;

    @Getter(onMethod = @__({
            @DynamoDbAttribute("sk"),
            @DynamoDbSortKey
    }))
    private String sk;

    private String id;

    private String idNoise;

    private Boolean active;

    @Getter(onMethod_ = {
            @DynamoDbAttribute("resources"),
            @DynamoDbConvertedBy(ResourceListConverter.class)
    })
    private List<DynamoDbResource> dynamoDbResources;

    @Getter(onMethod_ = {
            @DynamoDbAutoGeneratedTimestampAttribute,
            @DynamoDbUpdateBehavior(UpdateBehavior.WRITE_IF_NOT_EXISTS),
            @DynamoDbConvertedBy(InstantAsEpochMilliAttributeConverter.class)
    })
    private Instant createdDate;

    @Getter(onMethod_ = {
            @DynamoDbAutoGeneratedTimestampAttribute,
            @DynamoDbUpdateBehavior(UpdateBehavior.WRITE_ALWAYS),
            @DynamoDbConvertedBy(InstantAsEpochMilliAttributeConverter.class)
    })
    private Instant modifiedDate;

    public static DynamoDbAssetInfo newDynamoDbAssetInfo(CustomerKey customerKey) {
        Instant now = Instant.now();
        DynamoDbAssetInfo dynamoDbAssetInfo = new DynamoDbAssetInfo();
        dynamoDbAssetInfo.setId(customerKey.getValue());
        dynamoDbAssetInfo.setIdNoise(MD5Util.hash(customerKey.getValue()));
        dynamoDbAssetInfo.setActive(Boolean.TRUE);
        dynamoDbAssetInfo.setCreatedDate(now);
        dynamoDbAssetInfo.setModifiedDate(now);

        dynamoDbAssetInfo.setPk(customerKey.toDynamoDbKey());
        dynamoDbAssetInfo.setSk(Generators.timeBasedEpochGenerator().generate().toString());

        dynamoDbAssetInfo.setDynamoDbResources(new ArrayList<>());

        return dynamoDbAssetInfo;

    }

}
