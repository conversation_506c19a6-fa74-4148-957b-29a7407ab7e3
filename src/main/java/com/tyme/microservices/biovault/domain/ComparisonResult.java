package com.tyme.microservices.biovault.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ComparisonResult {

    @Builder.Default
    private Boolean result = false;

    private Integer score;

    private int maxScore;

    private int minimumScore;

    private boolean liveness;

    private String comparisonType;
}
