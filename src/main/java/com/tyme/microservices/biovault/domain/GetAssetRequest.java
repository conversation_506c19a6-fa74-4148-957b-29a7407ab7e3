package com.tyme.microservices.biovault.domain;

import com.tyme.microservices.biovault.controller.validator.CustomerKeyAnnotation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetAssetRequest {

    @NotNull
    @Valid
    @CustomerKeyAnnotation
    private CustomerKey customerKey;

    private GetAssetDetails details;

    public boolean isRequiredLatest() {
        return Optional.ofNullable(this.details)
                .map(GetAssetDetails::getIsRequiredLatest)
                .orElse(false);
    }

    public GetAssetDetails getAssetDetails() {
        if (null == this.details) {
            return GetAssetDetails.builder()
                    .type(GetAssetDetails.AssetRequestType.PHOTO)
                    .build();
        }

        return this.details;
    }
}
