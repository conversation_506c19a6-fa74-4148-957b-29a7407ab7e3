package com.tyme.microservices.biovault.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tyme.microservices.biovault.domain.enums.CustomerKeyType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AssetProviderRequest {

    @JsonProperty("customerKey")
    private ProviderCustomerKey customerKey;

    @JsonProperty("assetTypeRequest")
    private AssetProviderDetails assetProviderDetails;

    @Getter
    @Setter
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ProviderCustomerKey {

        private CustomerKeyType keyType;

        private String value;
    }
}
