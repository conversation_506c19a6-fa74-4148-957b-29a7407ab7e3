package com.tyme.microservices.biovault.domain;

import com.tyme.microservices.biovault.domain.enums.AssetProviderSourceType;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AssetProviderBioSource {

    private AssetProviderSourceType source;

    List<AssetProviderFallbackBioSource> fallbackBioSources;

}
