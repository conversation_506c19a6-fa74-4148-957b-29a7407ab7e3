package com.tyme.microservices.biovault.domain.enums;

import lombok.Getter;

@Getter
public enum CustomerKeyType {

    IDENTITY_NUMBER("identityNumber#"),
    PROFILE_ID("profileId#"),
    SA_ID("saId#"),
    DRIVER_LICENSE_ID("driverLicenseId#");

    CustomerKeyType(String prefixKey) {
        this.prefixKey = prefixKey;
    }

    private final String prefixKey;

    public static CustomerKeyType findByPk(String pk) {
        for (CustomerKeyType enumValue : CustomerKeyType.values()) {
            if (pk.startsWith(enumValue.prefixKey)) {
                return enumValue;
            }
        }
        throw new IllegalArgumentException("No enum constant with pk: " + pk);
    }

}
