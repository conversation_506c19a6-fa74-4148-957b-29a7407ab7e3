package com.tyme.microservices.biovault.domain;

import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbResource;
import com.tyme.microservices.biovault.domain.enums.CustomerKeyType;
import com.tyme.microservices.biovault.domain.enums.ResetDataType;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResetAssetResponse {
    private boolean status;
    private boolean bioResources;
    private List<String> baselines;
    private ResetFacialRecognitionResponse selfieCleanDeduplication;
}
