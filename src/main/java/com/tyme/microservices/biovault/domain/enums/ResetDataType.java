package com.tyme.microservices.biovault.domain.enums;

import com.tyme.microservices.biovault.domain.enums.CustomerKeyType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
public enum ResetDataType {

    ALL("ALL"), FACIAL_VERIFICATION("FACIAL_VERIFICATION"), BIO_VAULT("BIO_VAULT"), FACIAL_RECOGNITION("FACIAL_RECOGNITION");

    ResetDataType(String value) {
        this.value = value;
    }

    private final String value;

}
