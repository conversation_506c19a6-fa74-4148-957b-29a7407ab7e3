package com.tyme.microservices.biovault.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetAssetDetails {

    private AssetRequestType type;

    @Builder.Default
    private Boolean isRequiredLatest = false;

    @Builder.Default
    private List<PrioritySource> prioritySources = new ArrayList<>();

    public enum AssetRequestType {
        PHOTO, DATA
    }
}
