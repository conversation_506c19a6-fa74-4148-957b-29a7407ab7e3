package com.tyme.microservices.biovault.domain;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AssetProviderOperationResponse {

  private String externalId;
  private OperationFaceRegSession faceRegSession;


  @Getter
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  @ToString
  public static class OperationFaceRegSession {

    private String id;
    private Integer ageEstimateGroupEnumInt;
    private String externalDatabaseRefID;
    private Integer scanResultBlobSize;
    private Boolean success;
    private Boolean wasProcessed;
    private Boolean error;
    private CallMetadata callData;
    private OperationFaceMapCore data;

  }

  @Getter
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  @ToString
  public static class OperationFaceMapCore {

    private Integer faceMapSize;
    private Integer exportedFaceMapSize;
    private Integer auditTrailImageSize;

  }

  @Getter
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  @ToString
  public static class CallMetadata {

    private String tid;
    private String path;
    private String date;
    private String updatedDate;
    private long epochSecond;
    private String requestMethod;
    private String customID;

  }

}
