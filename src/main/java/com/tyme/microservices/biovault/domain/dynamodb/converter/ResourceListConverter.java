package com.tyme.microservices.biovault.domain.dynamodb.converter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbResource;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.enhanced.dynamodb.AttributeConverter;
import software.amazon.awssdk.enhanced.dynamodb.AttributeValueType;
import software.amazon.awssdk.enhanced.dynamodb.EnhancedType;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

import java.util.Collections;
import java.util.List;

@Log4j2
public class ResourceListConverter implements AttributeConverter<List<DynamoDbResource>> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public AttributeValue transformFrom(List<DynamoDbResource> input) {
        try {
            if (input != null) {

                return AttributeValue.builder().s(objectMapper.writeValueAsString(input)).build();
            }
        } catch (Exception ex){
            log.error(ex.getMessage(), ex);
        }

        return AttributeValue.builder().s(null).build();
    }

    @Override
    public List<DynamoDbResource> transformTo(AttributeValue input) {
        try {
            if (input != null && StringUtils.isNotEmpty(input.s())) {
                return objectMapper.readValue(input.s(), new TypeReference<List<DynamoDbResource>>() {});
            }
        } catch (Exception ex){
            log.error(ex.getMessage(), ex);
        }

        return Collections.emptyList();
    }

    @Override
    public EnhancedType<List<DynamoDbResource>> type() {
        return EnhancedType.listOf(DynamoDbResource.class);
    }

    @Override
    public AttributeValueType attributeValueType() {
        return AttributeValueType.S;
    }

}
