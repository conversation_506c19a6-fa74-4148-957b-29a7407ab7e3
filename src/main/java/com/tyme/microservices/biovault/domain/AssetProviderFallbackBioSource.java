package com.tyme.microservices.biovault.domain;

import com.tyme.microservices.biovault.domain.enums.AssetProviderSourceType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AssetProviderFallbackBioSource {

    private AssetProviderSourceType source;

    private int ordinal;

}
