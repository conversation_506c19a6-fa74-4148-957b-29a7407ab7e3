package com.tyme.microservices.biovault.domain;

import com.tyme.microservices.biovault.domain.enums.CustomerKeyType;
import com.tyme.microservices.biovault.domain.enums.ResetDataType;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResetAssetRequest {

    @NotNull(message = "idType is required")
    private CustomerKeyType idType;

    @NotNull(message = "id is required")
    private String id;

    private ResetDataType dataType;
}
