package com.tyme.microservices.biovault.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProviderPrioritySource {

    private String name;

    private int ordinal;

    @JsonProperty("isRequired")
    @Builder.Default
    private Boolean isRequired = false;
}
