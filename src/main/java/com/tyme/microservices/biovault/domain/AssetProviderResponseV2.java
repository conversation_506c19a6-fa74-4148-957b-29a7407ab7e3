package com.tyme.microservices.biovault.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.tyme.microservices.biovault.domain.enums.AssetProviderSourceType;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AssetProviderResponseV2 {

    private AssetProviderSourceType source;

    private ProviderDetails providerDetails;

    private List<Resource> resources;

    private List<AssetProviderResponseV2> fallbackSources;

}
