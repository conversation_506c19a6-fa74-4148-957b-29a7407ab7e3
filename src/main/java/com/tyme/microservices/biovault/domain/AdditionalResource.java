package com.tyme.microservices.biovault.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.tyme.microservices.biovault.domain.enums.AssetType;
import com.tyme.microservices.biovault.domain.enums.ResourceType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AdditionalResource {

    private String id;

    private AssetType type;

    private ResourceType resourceType;

    private String path;

    private String source;

    private AssetType from;

    @JsonProperty("hasValue")
    @Builder.Default
    private Boolean hasValue = false;

    @ToString.Exclude
    private String rawData;

    @ToString.Exclude
    private String rawPhoto;
}
