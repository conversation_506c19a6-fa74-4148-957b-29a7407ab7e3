package com.tyme.microservices.biovault.domain.dynamodb;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.tyme.microservices.biovault.domain.ComparisonResult;
import com.tyme.microservices.biovault.domain.enums.AssetType;
import com.tyme.microservices.biovault.domain.enums.BioSource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DynamoDbResource {

    private String id;

    private String path;

    private String type;

    private String source;

    private String versionId;

    @JsonProperty("hasValue")
    @Builder.Default
    private Boolean hasValue = false;

    private String rawData;

    private ComparisonResult comparisonResult;

    private Date createdDate;

    private Date modifiedDate;

    public static DynamoDbResource newResource(AssetType assetType, String source, String path) {
        Date now = new Date();
        DynamoDbResource dynamoDbResource = new DynamoDbResource();
        dynamoDbResource.setId(UUID.randomUUID().toString());
        dynamoDbResource.setType(assetType.name());
        dynamoDbResource.setHasValue(false);
        dynamoDbResource.setPath(path);
        if (null != source) {
            dynamoDbResource.setSource(source);
        }
        dynamoDbResource.setCreatedDate(now);
        dynamoDbResource.setModifiedDate(now);
        return dynamoDbResource;
    }
}
