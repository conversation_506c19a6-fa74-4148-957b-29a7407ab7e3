package com.tyme.microservices.biovault.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.tyme.microservices.biovault.domain.enums.ProviderStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetAssetResponse {

    private String id;

    private String session;

    @Builder.Default
    private List<Resource> resources = new ArrayList<>();

    @Builder.Default
    private List<ProviderDetails> provider = new ArrayList<>();

    public boolean isProviderDown() {
        if (null == provider || provider.isEmpty()) {
            return false;
        }
        return this.provider.stream().allMatch(e -> ProviderStatus.DOWN.equals(e.getStatus()));
    }
}
