package com.tyme.microservices.biovault.domain;

import com.tyme.microservices.biovault.controller.validator.CustomerKeyAnnotation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateAssetRequest {

    @NotNull
    @Valid
    @CustomerKeyAnnotation
    private CustomerKey customerKey;

    @Builder.Default
    private List<Resource> resources = new ArrayList<>();
}
