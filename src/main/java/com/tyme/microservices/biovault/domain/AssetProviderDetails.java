package com.tyme.microservices.biovault.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tyme.microservices.biovault.domain.enums.ProviderAssetType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssetProviderDetails {

    private ProviderAssetType type;

    @JsonProperty("prioritySources")
    @Builder.Default
    private List<ProviderPrioritySource> providerPrioritySources = new ArrayList<>();
}
