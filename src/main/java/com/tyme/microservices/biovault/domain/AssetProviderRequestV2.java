package com.tyme.microservices.biovault.domain;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AssetProviderRequestV2 {

    @NotNull
    private CustomerKey customerKey;

    @NotEmpty
    private List<AssetProviderBioSource> sources;
}