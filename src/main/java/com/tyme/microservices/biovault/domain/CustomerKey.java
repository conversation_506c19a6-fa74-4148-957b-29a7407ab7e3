package com.tyme.microservices.biovault.domain;

import com.tyme.microservices.biovault.domain.enums.CustomerKeyType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerKey {

    private CustomerKeyType keyType;

    private String value;

    public String toDynamoDbKey() {
        return this.keyType.getPrefixKey() + this.value;
    }
}
