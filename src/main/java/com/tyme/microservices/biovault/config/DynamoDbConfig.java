package com.tyme.microservices.biovault.config;

import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient;
import software.amazon.awssdk.enhanced.dynamodb.extensions.AutoGeneratedTimestampRecordExtension;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient;

import java.net.URI;

@Configuration
@Getter
@Log4j2
public class DynamoDbConfig extends BaseConfig {

    @Value("${infra.bio-vault.dynamodb.asset-info-table}")
    private String assetInfoTableName;

    public DynamoDbConfig(Environment environment) {
        super(environment);
    }

    @Bean
    public DynamoDbAsyncClient dynamoDbAsyncClient() {
        if (isProfileActive(localEnv)) {
            log.info("Create dynamoDbAsyncClientLocal");
            return DynamoDbAsyncClient.builder()
                    .region(Region.of(region))
                    .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(localAccessKey,localSecretKey)))
                    .endpointOverride(URI.create(endpoint))
                    .build();
        }
        if (isProfileActive(testEnv)) {
            log.info("Create dynamoDbAsyncClientTest");
            region = environment.getProperty("cloud.aws.region.static");
            endpoint = environment.getProperty("cloud.aws.dynamodb.endpoint");
            String testAccessKey = environment.getProperty("cloud.aws.credentials.access-key");
            String testSecretKey = environment.getProperty("cloud.aws.credentials.secret-key");
            AwsBasicCredentials awsBasicCredentials = AwsBasicCredentials.create(testAccessKey, testSecretKey);
            return DynamoDbAsyncClient.builder()
                    .region(Region.of(region))
                    .credentialsProvider(StaticCredentialsProvider.create(awsBasicCredentials))
                    .endpointOverride(URI.create(endpoint))
                    .build();
        }
        log.info("Create dynamoDbAsyncClientClient");
        return DynamoDbAsyncClient.builder()
                .region(Region.of(region))
                .build();
    }

    @Bean
    public DynamoDbEnhancedAsyncClient dynamoDbEnhancedAsyncClient() {
        return DynamoDbEnhancedAsyncClient
                .builder()
                .dynamoDbClient(dynamoDbAsyncClient())
                .extensions(AutoGeneratedTimestampRecordExtension.create())
                .build();
    }

}
