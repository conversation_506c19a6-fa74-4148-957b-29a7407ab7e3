package com.tyme.microservices.biovault.config;

import com.tyme.microservices.biovault.domain.ResultWrapper;
import reactor.core.publisher.Mono;

public class MonoWrapError {

    private MonoWrapError() {

    }

    public static <T> Mono<ResultWrapper<T>>  wrapError(Mono<T> momo) {
        return momo
                .map(res -> new ResultWrapper<T>(res))
                .onErrorResume(e -> Mono.just(new ResultWrapper<T>(e)));
    }

}
