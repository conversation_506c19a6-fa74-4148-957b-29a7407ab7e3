package com.tyme.microservices.biovault.config;

import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3AsyncClient;

import java.net.URI;

@Getter
@Configuration
@Log4j2
public class S3ClientConfig extends BaseConfig {

    @Value("${infra.bio-vault.s3.bucket-name}")
    private String bucketName;

    @Value("${infra.bio-vault.s3.baseline-bucket-name}")
    private String baselineBucketName;

    @Value("${infra.bio-vault.s3.photo-folder-path-format}")
    private String photoFolderPathFormat;

    public S3ClientConfig(Environment environment) {
        super(environment);
    }

    @Bean
    public S3AsyncClient s3AsyncClient() {
        if (isProfileActive(localEnv)) {
            log.info("Create s3AsyncClientLocal");
            return S3AsyncClient.builder()
                    .region(Region.of(region))
                    .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(localAccessKey,localSecretKey)))
                    .endpointOverride(URI.create(endpoint))
                    .build();
        }
        if (isProfileActive(testEnv)) {
            log.info("Create s3ClientTest");
            region = environment.getProperty("cloud.aws.region.static");
            endpoint = environment.getProperty("cloud.aws.s3.endpoint");
            String testAccessKey = environment.getProperty("cloud.aws.credentials.access-key");
            String testSecretKey = environment.getProperty("cloud.aws.credentials.secret-key");
            AwsBasicCredentials awsBasicCredentials = AwsBasicCredentials.create(testAccessKey, testSecretKey);
            return S3AsyncClient.builder()
                    .region(Region.of(region))
                    .credentialsProvider(StaticCredentialsProvider.create(awsBasicCredentials))
                    .endpointOverride(URI.create(endpoint))
                    .build();
        }
        log.info("Create s3Client");
        return S3AsyncClient.builder()
                .region(Region.of(region))
                .build();
    }

}
