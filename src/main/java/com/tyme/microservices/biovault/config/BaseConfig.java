package com.tyme.microservices.biovault.config;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.util.stream.Stream;

@Configuration
@Data
@RequiredArgsConstructor
public class BaseConfig {

    protected final Environment environment;

    protected final String testEnv = "test";
    protected final String localEnv = "local";

    protected final String localAccessKey = "accesskey";
    protected final String localSecretKey = "secretkey";

    @Value("${infra.bio-vault.region:eu-west-1}")
    public String region;

    @Value("${infra.bio-vault.endpoint:http://localhost:4566}")
    public String endpoint;

    public boolean isProfileActive(String profile) {
        return Stream
                .of(environment.getActiveProfiles())
                .anyMatch(profile::equalsIgnoreCase);
    }
}
