package com.tyme.microservices.biovault.controller;


import com.tyme.microservices.biovault.domain.GetAssetRequest;
import com.tyme.microservices.biovault.domain.GetAssetResponse;
import com.tyme.microservices.biovault.domain.ResetAssetRequest;
import com.tyme.microservices.biovault.domain.ResetAssetResponse;
import com.tyme.microservices.biovault.service.BioAssetOperationService;
import com.tyme.microservices.biovault.service.BioAssetService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.Map;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "bio-vault-operation")
@RestController
@RequestMapping("/bio-vault-operation")
@Log4j2
@Profile("!prd")
public class BioAssetOperationController {

    private final BioAssetService bioAssetService;
    private final BioAssetOperationService bioAssetOperationService;

    @Autowired
    public BioAssetOperationController(BioAssetService bioAssetService,
        BioAssetOperationService bioAssetOperationService) {
        this.bioAssetService = bioAssetService;
      this.bioAssetOperationService = bioAssetOperationService;
    }

    @PostMapping("/reset")
    public Mono<ResetAssetResponse> resetAsset(@Valid @RequestBody ResetAssetRequest resetAssetRequest) {
        log.info("method: resetAsset - start");
        return this.bioAssetService.resetAsset(resetAssetRequest);
    }

    @PostMapping("/get-bio-resource")
    public Mono<Map<String, Object>> getBioResource(@Valid @RequestBody ResetAssetRequest resetAssetRequest) {
        log.info("method: getBioResource - start");
        return this.bioAssetOperationService.getBioResource(resetAssetRequest);
    }
}
