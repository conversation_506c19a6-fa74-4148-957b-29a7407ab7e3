package com.tyme.microservices.biovault.controller.validator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;

@Constraint(validatedBy = CustomerKeyValidator.class)
@Target({ METHOD, FIELD, PARAMETER, TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CustomerKeyAnnotation {

    Class<?>[] groups() default {};

    String message() default "Bad request";

    Class<? extends Payload>[] payload() default {};
}
