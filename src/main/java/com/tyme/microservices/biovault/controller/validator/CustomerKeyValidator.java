package com.tyme.microservices.biovault.controller.validator;

import com.tyme.microservices.biovault.domain.CustomerKey;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.regex.Pattern;

public class CustomerKeyValidator implements ConstraintValidator<CustomerKeyAnnotation, CustomerKey> {

    private static final Pattern PROFILE_ID_PATTERN = Pattern.compile("^[a-zA-Z0-9]{32}$");

    private static final long SA_ID_LENGTH = 13;

    @Override
    public boolean isValid(CustomerKey customerKey, ConstraintValidatorContext context) {
        if (null == customerKey || null == customerKey.getKeyType() || null == customerKey.getValue()) {
            return false;
        }
        return switch (customerKey.getKeyType()) {
            case SA_ID -> isValidSaId(customerKey.getValue());
            case PROFILE_ID -> isValidProfileId(customerKey.getValue());
            default -> false;
        };
    }

    private boolean isValidProfileId(String profileId) {
        return PROFILE_ID_PATTERN.matcher(profileId).matches();
    }

    private boolean isValidSaId(String saId) {
        if (saId == null) {
            return false;
        }
        boolean is13Digits = is13Digits(saId);
        boolean is11thValid = is11thValid(saId);
        boolean isValidateMOD10AndCDVFormat = isValidateMOD10AndCDVFormat(saId);

        return is11thValid && isValidateMOD10AndCDVFormat && is13Digits;
    }

    private boolean is11thValid(String saId) {
        try {
            int eleventhOfSaIdNumber = Integer.parseInt(saId.substring(10, 11));
            return Arrays.asList(0, 1, 2).contains(eleventhOfSaIdNumber);
        } catch (NumberFormatException | StringIndexOutOfBoundsException ex) {
            return false;
        }

    }

    private boolean is13Digits(String saId) {
        try {
            return StringUtils.isNumeric(saId) && saId.length() == SA_ID_LENGTH;
        } catch (NumberFormatException ex) {
            return false;
        }

    }

    private boolean isValidateMOD10AndCDVFormat(String saId) {
        int sum = 0;
        try {
            for (int i = 0; i < saId.length() - 1; i++) {
                char ch = saId.charAt(saId.length() - i - 2);
                int digit = ch - 48;
                int weight;

                if (i % 2 == 0) {
                    weight = (2 * digit) - digit / 5 * 9;
                } else {
                    weight = digit;
                }
                sum += weight;
            }
            sum = Math.abs(sum) + 10;
            int lastDigitOfIdNumber = Integer.parseInt(saId.substring(saId.length() - 1));
            int generatedNumber = (10 - (sum % 10)) % 10;
            return generatedNumber == lastDigitOfIdNumber;
        } catch (NumberFormatException | StringIndexOutOfBoundsException ex) {
            return false;
        }
    }
}
