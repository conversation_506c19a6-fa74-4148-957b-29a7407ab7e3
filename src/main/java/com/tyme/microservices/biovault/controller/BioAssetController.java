package com.tyme.microservices.biovault.controller;


import com.tyme.microservices.biovault.domain.GetAssetRequest;
import com.tyme.microservices.biovault.domain.GetAssetResponse;
import com.tyme.microservices.biovault.domain.UpdateAssetRequest;
import com.tyme.microservices.biovault.domain.UpdateAssetResponse;
import com.tyme.microservices.biovault.service.BioAssetService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "bio-vault")
@RestController
@RequestMapping("${app.endpoint.prefix.bio-asset}")
@Log4j2
public class BioAssetController {

    private final BioAssetService bioAssetService;

    @Autowired
    public BioAssetController(BioAssetService bioAssetService) {
        this.bioAssetService = bioAssetService;
    }

    @PostMapping("/assets/search")
    public Mono<GetAssetResponse> searchAsset(@Valid @RequestBody GetAssetRequest getAssetRequest) {
        log.info("method: searchAsset - start");
        return this.bioAssetService.searchAsset(getAssetRequest);
    }

    @PatchMapping("/assets")
    public Mono<UpdateAssetResponse> updateAsset(@Valid @RequestBody UpdateAssetRequest updateAssetRequest) {
        log.info("method: updateAsset - start");
        return this.bioAssetService.updateAsset(updateAssetRequest);
    }
}
