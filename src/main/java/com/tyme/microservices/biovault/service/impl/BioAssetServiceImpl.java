package com.tyme.microservices.biovault.service.impl;

import com.fasterxml.uuid.Generators;
import com.tyme.microservices.biovault.config.MonoWrapError;
import com.tyme.microservices.biovault.config.PhotoConfig;
import com.tyme.microservices.biovault.config.S3ClientConfig;
import com.tyme.microservices.biovault.config.md5.MD5Util;
import com.tyme.microservices.biovault.domain.AdditionalResource;
import com.tyme.microservices.biovault.domain.AssetProviderDetails;
import com.tyme.microservices.biovault.domain.AssetProviderRequest;
import com.tyme.microservices.biovault.domain.AssetProviderResponse;
import com.tyme.microservices.biovault.domain.CustomerKey;
import com.tyme.microservices.biovault.domain.GetAssetDetails;
import com.tyme.microservices.biovault.domain.GetAssetRequest;
import com.tyme.microservices.biovault.domain.GetAssetResponse;
import com.tyme.microservices.biovault.domain.PrioritySource;
import com.tyme.microservices.biovault.domain.ProviderDetails;
import com.tyme.microservices.biovault.domain.ProviderPrioritySource;
import com.tyme.microservices.biovault.domain.ResetAssetRequest;
import com.tyme.microservices.biovault.domain.ResetAssetResponse;
import com.tyme.microservices.biovault.domain.ResetBaselineRequest;
import com.tyme.microservices.biovault.domain.ResetBaselineResponse;
import com.tyme.microservices.biovault.domain.ResetFacialRecognitionRequest;
import com.tyme.microservices.biovault.domain.ResetFacialRecognitionResponse;
import com.tyme.microservices.biovault.domain.Resource;
import com.tyme.microservices.biovault.domain.ResultWrapper;
import com.tyme.microservices.biovault.domain.UpdateAssetRequest;
import com.tyme.microservices.biovault.domain.UpdateAssetResponse;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbAssetInfo;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbKey;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbResource;
import com.tyme.microservices.biovault.domain.enums.AssetType;
import com.tyme.microservices.biovault.domain.enums.BioSource;
import com.tyme.microservices.biovault.domain.enums.CustomerKeyType;
import com.tyme.microservices.biovault.domain.enums.MissingDbResourceType;
import com.tyme.microservices.biovault.domain.enums.ProviderAssetType;
import com.tyme.microservices.biovault.domain.enums.ResourceType;
import com.tyme.microservices.biovault.exception.PhotoException;
import com.tyme.microservices.biovault.service.BioAssetProviderService;
import com.tyme.microservices.biovault.service.BioAssetService;
import com.tyme.microservices.biovault.service.connector.BioAssetProviderConnector;
import com.tyme.microservices.biovault.service.connector.DynamoDbConnector;
import com.tyme.microservices.biovault.service.connector.FacialRecognitionConnector;
import com.tyme.microservices.biovault.service.connector.FacialVerificationConnector;
import com.tyme.microservices.biovault.service.connector.ProfileConnector;
import com.tyme.microservices.biovault.service.connector.S3Connector;
import com.tyme.microservices.biovault.service.storage.S3FaceStorage;
import com.tyme.microservices.biovault.util.FileNameUtils;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple3;
import software.amazon.awssdk.services.s3.model.CopyObjectResponse;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Log4j2
public class BioAssetServiceImpl implements BioAssetService {

    private final DynamoDbConnector dynamoDbConnector;

    private final S3FaceStorage s3FaceStorage;

    private final S3ClientConfig s3ClientConfig;

    private final BioAssetProviderConnector bioAssetProviderConnector;

    private final BioAssetProviderService bioAssetProviderService;

    private final S3Connector s3Connector;

    private final PhotoConfig photoConfig;

    private final FacialVerificationConnector facialVerificationConnector;

    private final FacialRecognitionConnector facialRecognitionConnector;

    private final ProfileConnector profileConnector;

    @Override
    public Mono<GetAssetResponse> searchAsset(GetAssetRequest getAssetRequest) {
        log.info("method: searchAsset - getAssetRequest: {}", getAssetRequest);
        return this.dynamoDbConnector.getLatestAssetInfo(getAssetRequest.getCustomerKey().toDynamoDbKey())
                .defaultIfEmpty(DynamoDbAssetInfo.newDynamoDbAssetInfo(getAssetRequest.getCustomerKey()))
                .flatMap(asset -> this.processAssetInfo(asset, getAssetRequest))
                .doOnSuccess(e -> log.info("method: searchAsset - success"))
                .doOnError(ex -> log.error("method: searchAsset - failed, ex: {}", ex.getMessage(), ex));
    }

    private Mono<GetAssetResponse> processAssetInfo(DynamoDbAssetInfo info, GetAssetRequest getAssetRequest) {
        log.info("method: processAssetInfo - info pk: {}, sk: {}, assetRequest: {}", info.getPk(), info.getSk(), getAssetRequest);
        Mono<DynamoDbAssetInfo> dynamoDbAssetInfoMono = this.getOrCloneAssetInfo(info, getAssetRequest)
                .flatMap(assetInfo -> this.fulfillResource(assetInfo, getAssetRequest));

        boolean isProviderRequired = this.requireFromProvider(info.getDynamoDbResources(), getAssetRequest);
        List<AssetType> assetTypes = this.getAssetTypeRequest(getAssetRequest);
        log.info("method: processAssetInfo - request assetTypes: {}", assetTypes);

        if (!isProviderRequired) {
            log.info("method: processAssetInfo - no need to call provider");
            return dynamoDbAssetInfoMono
                    .flatMap(assetInfo -> this.buildBioAssetResponse(info, assetTypes, new HashMap<>(), Collections.emptyList()))
                    .doOnSuccess(e -> log.info("method: processAssetInfo - using internal data return successfully"))
                    .doOnError(error -> log.error("method: processAssetInfo - using internal data return failed"));
        } else {
            log.info("method: processAssetInfo - need to call provider");
            Mono<AssetProviderResponse> assetProviderResponseMono = this.getAssetProvider(info.getDynamoDbResources(), getAssetRequest);
            return Mono.zip(dynamoDbAssetInfoMono, assetProviderResponseMono)
                    .flatMap(tuple -> {
                        DynamoDbAssetInfo assetInfo = tuple.getT1();
                        AssetProviderResponse assetProviderResponse = tuple.getT2();
                        log.info("method: processAssetInfo - provider response {}", assetProviderResponse);
                        log.info("method: processAssetInfo - dynamoDbAssetInfo to update {}", assetInfo);
                        // this map contains additional information in another resource that should be separated to stored as an independent resource
                        Map<String, AdditionalResource> additionalResourceMap = new HashMap<>();
                        // this map contains the mapping between db resource and resource provider
                        Map<String, Resource> idResourceProviderMap = new HashMap<>();

                        return this.mergeResourceFromProvider(assetInfo, assetProviderResponse.getResources(), additionalResourceMap, idResourceProviderMap)
                                .then(Mono.defer(() -> {
                                    log.info("method: processAssetInfo - dynamoDbAssetInfo updated: {}", assetInfo);


                                    return Mono.just(assetInfo)
                                            .flatMap(data -> this.concludeAdditionalResources(data, additionalResourceMap))
                                            .flatMap(dynamoDbConnector::save)
                                            .then(Mono.defer(() -> {
                                                if (Boolean.TRUE.equals(getAssetRequest.isRequiredLatest())) {
                                                    log.info("method: processAssetInfo - return with latest response from provider");
                                                    return this.buildBioAssetResponseLatest(assetInfo, idResourceProviderMap, assetProviderResponse.getProvider());
                                                }

                                                return this.buildBioAssetResponse(assetInfo, assetTypes, additionalResourceMap, assetProviderResponse.getProvider());
                                            }));
                                }))
                                .doOnSuccess(e -> log.info("process merge and save success"))
                                .doOnError(ex -> log.error("process merge and save failed ex: {}", ex.getMessage(), ex));
                    })
                    .doOnSuccess(e -> log.info("method: processAssetInfo - get resource from provider and save success"))
                    .doOnError(ex -> log.error("method: processAssetInfo - handle response and save failed ex: {}", ex.getMessage(), ex));
        }
    }

    private List<AssetType> getAssetTypeRequest(GetAssetRequest getAssetRequest) {
        List<AssetType> assetTypes = new ArrayList<>();
        if (GetAssetDetails.AssetRequestType.PHOTO.equals(getAssetRequest.getAssetDetails().getType())) {
            assetTypes.addAll(AssetType.PHOTO_RESOURCES);
        } else if (GetAssetDetails.AssetRequestType.DATA.equals(getAssetRequest.getAssetDetails().getType())) {
            assetTypes.add(AssetType.DATA);
        }
        return assetTypes;
    }

    public Mono<DynamoDbAssetInfo> concludeAdditionalResources(DynamoDbAssetInfo assetInfo, Map<String, AdditionalResource> additionalResourceMap) {
        log.info("method: processAssetInfo: additionalResourcesMap: {}", additionalResourceMap);
        if (CollectionUtils.isEmpty(additionalResourceMap)) {
            return Mono.just(assetInfo);
        }

        List<DynamoDbResource> dynamoDbResources = assetInfo.getDynamoDbResources();
        if (null == dynamoDbResources) {
            dynamoDbResources = new ArrayList<>();
        }
        Map<String, DynamoDbResource> dynamoDbResourceMap = dynamoDbResources.stream()
                .collect(Collectors.toMap(r -> this.buildResourceKey(r.getType(), r.getSource()), Function.identity()));

        AdditionalResource photo2DResource = additionalResourceMap.get(AssetType.PHOTO_2D.name());
        if (null != photo2DResource && StringUtils.isNotBlank(photo2DResource.getRawPhoto())) {
            String path = this.buildS3PhotoPath(assetInfo.getIdNoise(), assetInfo.getSk(), FileNameUtils.getFileName(AssetType.PHOTO_2D, photo2DResource.getSource()));
            String key = dynamoDbResourceMap.containsKey(photo2DResource.getType().name()) ? photo2DResource.getType().name() : this.buildResourceKey(photo2DResource.getType().name(), photo2DResource.getSource());
            Optional<DynamoDbResource> dynamoDbResourceOptional = Optional.ofNullable(dynamoDbResourceMap.get(key));
            return dynamoDbResourceOptional
                    .map(dbResource -> this.s3Connector.putObject(s3ClientConfig.getBucketName(), path, Base64.getDecoder().decode(photo2DResource.getRawPhoto()))
                            .doOnSuccess(e -> {
                                dbResource.setPath(path);
                                dbResource.setSource(photo2DResource.getSource());
                                dbResource.setHasValue(true);
                                dbResource.setVersionId(e.versionId());
                                log.info("update PHOTO_2D path: {}, versionId: {}", path, e.versionId());
                            }).thenReturn(assetInfo))
                    .orElseGet(() -> this.s3Connector.putObject(s3ClientConfig.getBucketName(), path, Base64.getDecoder().decode(photo2DResource.getRawPhoto()))
                            .flatMap(e -> {
                                DynamoDbResource dynamoDbResource = DynamoDbResource.newResource(AssetType.PHOTO_2D, photo2DResource.getSource(), path);
                                dynamoDbResource.setPath(path);
                                dynamoDbResource.setSource(photo2DResource.getSource());
                                dynamoDbResource.setHasValue(true);
                                dynamoDbResource.setVersionId(e.versionId());
                                assetInfo.getDynamoDbResources().add(dynamoDbResource);
                                log.info("add PHOTO_2D path: {}, versionId: {}", path, e.versionId());
                                return Mono.just(assetInfo);
                            }));
        }

        return Mono.just(assetInfo);
    }

    private Mono<AssetProviderResponse> getAssetProvider(List<DynamoDbResource> dynamoDbResources, GetAssetRequest getAssetRequest) {
        GetAssetDetails getAssetDetails = getAssetRequest.getAssetDetails();
        if (GetAssetDetails.AssetRequestType.PHOTO.equals(getAssetDetails.getType())) {
            if (isMissingPhoto(dynamoDbResources)) {
                log.info("getFaceMapFallbackPhoto2DFromProvider");
                return bioAssetProviderService.getFaceMapFallbackPhoto2DFromProvider(getAssetRequest.getCustomerKey());
            } else {
                log.info("getFaceMapFromProvider");
                return bioAssetProviderService.getFaceMapFromProvider(getAssetRequest.getCustomerKey());
            }
        } else if (GetAssetDetails.AssetRequestType.DATA.equals(getAssetDetails.getType())) {
            return this.getDataFromProvider(getAssetRequest);
        }

        return Mono.empty();
    }

    private boolean requireFromProvider(List<DynamoDbResource> dynamoDbResources, GetAssetRequest getAssetRequest) {
        GetAssetDetails getAssetDetails = getAssetRequest.getAssetDetails();
        Map<MissingDbResourceType, Boolean> missingDbResourceTypeMap = new HashMap<>();
        switch (getAssetDetails.getType()) {
            case PHOTO -> {
                if (isMissingPhoto(dynamoDbResources)) {
                    return true;
                }
                if (isMissingFaceMap(dynamoDbResources)) {
                    return true;
                }
            }
            case DATA -> {
                if (Boolean.TRUE.equals(getAssetDetails.getIsRequiredLatest())) {
                    return true;
                } else if (isMissingData(dynamoDbResources, getAssetDetails.getPrioritySources().stream().map(PrioritySource::getName).toList()))
                    return true;
            }
        }

        return false;
    }

    private Mono<DynamoDbAssetInfo> getOrCloneAssetInfo(DynamoDbAssetInfo info, GetAssetRequest getAssetRequest) {
        GetAssetDetails getAssetDetails = getAssetRequest.getAssetDetails();
        if (GetAssetDetails.AssetRequestType.PHOTO.equals(getAssetDetails.getType())) {
            Optional<DynamoDbResource> resource2DOptional = info.getDynamoDbResources().stream()
                    .filter(r -> AssetType.PHOTO_2D.name().equalsIgnoreCase(r.getType()))
                    .findFirst();
            if (resource2DOptional.isPresent() && isResourceExpired(resource2DOptional.get(), photoConfig.getExpiredTime2D())) {
                return this.copyToNewSession(info, List.of(resource2DOptional.get().getId()));
            }
        }
        return Mono.just(info);
    }

    private Mono<DynamoDbAssetInfo> fulfillResource(DynamoDbAssetInfo assetInfo, GetAssetRequest getAssetRequest) {
        GetAssetDetails getAssetDetails = getAssetRequest.getAssetDetails();
        if (GetAssetDetails.AssetRequestType.PHOTO.equals(getAssetDetails.getType())) {
            this.fulfillGetPhotoRequest(assetInfo);
        } else if (GetAssetDetails.AssetRequestType.DATA.equals(getAssetDetails.getType())) {
            this.fulfillGetDataRequest(assetInfo, getAssetRequest);
        }

        return this.dynamoDbConnector.save(assetInfo)
                .thenReturn(assetInfo);
    }

    private void fulfillGetPhotoRequest(DynamoDbAssetInfo assetInfo) {
        List<DynamoDbResource> resources = assetInfo.getDynamoDbResources();
        if (null == resources) {
            resources = new ArrayList<>();
        }

        List<DynamoDbResource> newResources = new ArrayList<>();
        for (AssetType assetType : AssetType.PHOTO_RESOURCES) {
            switch (assetType) {
                case FACE_MAP -> {
                    for (BioSource source : List.of(BioSource.FACETEC_COMPARISON, BioSource.FACETEC_LIVENESS_CHECK)) {
                        resources.stream()
                                .filter(r -> assetType.name().equalsIgnoreCase(r.getType()) && source.name().equalsIgnoreCase(r.getSource()))
                                .findFirst().ifPresentOrElse(r -> log.info("FACE_MAP - {} is existing", source),
                                        () -> {
                                            log.info("FaceMapBioSource not found assetType: {}, source: {}, add new", assetType.name(), source.name());
                                            newResources.add(DynamoDbResource.newResource(assetType, source.name(),
                                                this.buildS3PhotoPath(assetInfo.getIdNoise(), assetInfo.getSk(), FileNameUtils.getFileName(AssetType.FACE_MAP, source.name()))));
                                        });
                    }
                }
                case PHOTO_2D -> resources.stream()
                        .filter(r -> assetType.name().equalsIgnoreCase(r.getType()))
                        .findFirst().ifPresentOrElse(r -> log.info("PHOTO_2D is existing"),
                                () -> {
                                    log.info("BioSource not found assetType: {}, add new", assetType.name());
                                    newResources.add(DynamoDbResource.newResource(assetType, BioSource.UNKNOWN.name(),
                                                    this.buildS3PhotoPath(assetInfo.getIdNoise(), assetInfo.getSk(), FileNameUtils.getFileName(AssetType.PHOTO_2D, BioSource.UNKNOWN.name()))
                                            )
                                    );
                                }
                        );
                case SELFIE -> {
                    for (BioSource source : List.of(BioSource.CHANNEL, BioSource.FACETEC_AUDIT_TRAIL)) {
                        resources.stream()
                            .filter(r -> assetType.name().equalsIgnoreCase(r.getType()) && source.name().equalsIgnoreCase(r.getSource()))
                            .findFirst().ifPresentOrElse(r -> log.info("SELFIE is existing source: {}", source.name()),
                                () -> {
                                    log.info("SelfieBioSource not found assetType: {}, source: {}, add new", assetType.name(), source.name());
                                    newResources.add(DynamoDbResource.newResource(assetType,
                                        source.name(),
                                        this.buildS3PhotoPath(assetInfo.getIdNoise(), assetInfo.getSk(),
                                            FileNameUtils.getFileName(AssetType.SELFIE, source.name()))));
                                });
                    }
                }
                default -> log.info("nothing");
            }
        }

        resources.addAll(newResources);
    }

    private void fulfillGetDataRequest(DynamoDbAssetInfo assetInfo, GetAssetRequest getAssetRequest) {
        List<DynamoDbResource> resources = assetInfo.getDynamoDbResources();
        if (null == resources) {
            resources = new ArrayList<>();
        }

        List<DynamoDbResource> newResources = new ArrayList<>();
        List<PrioritySource> prioritySources = getAssetRequest.getAssetDetails().getPrioritySources();
        for (PrioritySource source : prioritySources) {
            Optional<DynamoDbResource> resourceOptional = resources.stream()
                    .filter(r -> AssetType.DATA.name().equalsIgnoreCase(r.getType()) && source.getName().equalsIgnoreCase(r.getSource()))
                    .findFirst();
            if (resourceOptional.isEmpty()) {
                newResources.add(DynamoDbResource.newResource(AssetType.DATA, source.getName(), null));
            }
        }
        resources.addAll(newResources);
    }

    private boolean isMissingPhoto(List<DynamoDbResource> dynamoDbResources) {
        boolean isMissing = dynamoDbResources.stream()
                .filter(r -> AssetType.PHOTO_2D.name().equalsIgnoreCase(r.getType()))
                .allMatch(r -> Boolean.FALSE.equals(r.getHasValue()));
        log.info("method: isMissingPhoto: {}", isMissing);

        return isMissing;
    }

    private boolean isMissingFaceMap(List<DynamoDbResource> dynamoDbResources) {
        boolean isMissing = dynamoDbResources.stream()
                .filter(r -> AssetType.FACE_MAP.name().equalsIgnoreCase(r.getType()) && BioSource.FACETEC_COMPARISON.name().equalsIgnoreCase(r.getSource()))
                .allMatch(r -> Boolean.FALSE.equals(r.getHasValue()));
        log.info("method: isMissingFaceMap: {}", isMissing);

        return isMissing;
    }

    private boolean isMissingData(List<DynamoDbResource> dynamoDbResources, List<String> sources) {
        boolean isMissing = dynamoDbResources.stream()
                .filter(r -> AssetType.DATA.name().equalsIgnoreCase(r.getType()) && sources.contains(r.getSource()))
                .allMatch(r -> Boolean.FALSE.equals(r.getHasValue()));
        log.info("method: isMissingData: {}", isMissing);

        return isMissing;
    }

    private Mono<AssetProviderResponse> getDataFromProvider(GetAssetRequest getAssetRequest) {
        List<ProviderPrioritySource> providerPrioritySources = new ArrayList<>();
        for (PrioritySource prioritySource : getAssetRequest.getAssetDetails().getPrioritySources()) {
            providerPrioritySources.add(ProviderPrioritySource.builder()
                    .name(prioritySource.getName())
                    .ordinal(prioritySource.getOrdinal())
                    .isRequired(prioritySource.getIsRequired()).build());
        }

        AssetProviderRequest assetProviderRequest = AssetProviderRequest.builder()
                .customerKey(AssetProviderRequest.ProviderCustomerKey.builder()
                        .keyType(getAssetRequest.getCustomerKey().getKeyType())
                        .value(getAssetRequest.getCustomerKey().getValue()).build())
                .assetProviderDetails(AssetProviderDetails.builder()
                        .type(ProviderAssetType.DATA)
                        .providerPrioritySources(providerPrioritySources).build())
                .build();
        log.info("method: getLatestDataFromProvider - assetProviderRequest: {}", assetProviderRequest);

        return this.bioAssetProviderConnector.getAssetFromProvider(assetProviderRequest);
    }

    private Mono<Void> mergeResourceFromProvider(DynamoDbAssetInfo dynamoDbAssetInfo,
                                                 List<Resource> resourceProviders,
                                                 Map<String, AdditionalResource> addtionalResourceMap,
                                                 Map<String, Resource> idResourceMap) {
        List<DynamoDbResource> dynamoDbResources = dynamoDbAssetInfo.getDynamoDbResources();
        Map<String, DynamoDbResource> resourceMap = dynamoDbResources.stream()
                .collect(Collectors.toMap(r -> this.buildResourceKey(r.getType(), r.getSource()), Function.identity()));
        log.info("resourceMap: {}", resourceMap);

        return Flux.fromIterable(resourceProviders)
                .flatMap(r -> {
                    DynamoDbResource resourceDb = (null != resourceMap.get(r.getType().name())) ?
                            resourceMap.get(r.getType().name()) :
                            resourceMap.get(this.buildResourceKey(r.getType().name(), r.getSource()));
                    if (null == resourceDb) {
                        log.info("method: mergeResourceFromProvider - no resource in db matching to process update for {} - {}", r.getType().name(), r.getSource());
                        return Mono.empty();
                    }
                    log.info("method: mergeResourceFromProvider - process update for {} - {}", r.getType().name(), r.getSource());

                    return Mono.just(resourceDb)
                            .zipWith(Mono.just(r))
                            .flatMap(tuple -> {
                                DynamoDbResource dynamoDbResource = tuple.getT1();
                                Resource resourceProvider = tuple.getT2();
                                idResourceMap.put(dynamoDbResource.getId(), resourceProvider);
                                log.info("method: mergeResourceFromProvider - dynamoDbResource: {}", dynamoDbResource);
                                log.info("method: mergeResourceFromProvider - resourceProvider: {}", resourceProvider);
                                if (Boolean.TRUE.equals(r.getHasValue())) {
                                    String path;
                                    log.warn("handle resource type {} - {}", resourceProvider.getResourceType(), resourceProvider.getSource());
                                    switch (resourceProvider.getResourceType()) {
                                        case RAW_BASE64:
                                            log.info("handle raw base64 resource {} - {}", resourceProvider.getResourceType(), resourceProvider.getSource());
                                            String fileName = FileNameUtils.getFileName(resourceProvider.getType(), resourceProvider.getSource());
                                            path = String.format(s3ClientConfig.getPhotoFolderPathFormat(),
                                                    dynamoDbAssetInfo.getIdNoise(), dynamoDbAssetInfo.getSk(), fileName);
                                            log.info("method: mergeResourceFromProvider - start upload photo to S3: {}", path);
                                            return this.s3FaceStorage.uploadPhoto(path, Base64.getDecoder().decode(resourceProvider.getRawPhoto()))
                                                    .doOnSuccess(e -> {
                                                        dynamoDbResource.setPath(path);
                                                        dynamoDbResource.setSource(resourceProvider.getSource());
                                                        dynamoDbResource.setHasValue(true);
                                                    })
                                                    .then();
                                        case S3_ENDPOINT:
                                            log.info("handle s3 end point resource {} - {}", resourceProvider.getResourceType(), resourceProvider.getSource());
                                            path = String.format(s3ClientConfig.getPhotoFolderPathFormat(),
                                                    dynamoDbAssetInfo.getIdNoise(), dynamoDbAssetInfo.getSk(), FileNameUtils.getBaselineFileName(resourceProvider.getPath()));
                                            return this.s3Connector.isFileExisted(s3ClientConfig.getBaselineBucketName(), resourceProvider.getPath())
                                                    .then(this.s3Connector.copyObject(s3ClientConfig.getBaselineBucketName(), s3ClientConfig.getBucketName(), resourceProvider.getPath(), path)
                                                            .doOnSuccess(e -> {
                                                                dynamoDbResource.setPath(path);
                                                                dynamoDbResource.setSource(resourceProvider.getSource());
                                                                dynamoDbResource.setHasValue(true);
                                                            })
                                                            .then())
                                                    .doOnSuccess(success -> log.info("process copy success path: {} to path: {}", resourceProvider.getPath(), path))
                                                    .onErrorResume(ex -> {
                                                        log.info("process copy failed from path: {} to path: {}", resourceProvider.getPath(), path);
                                                        return Mono.empty();
                                                    });
                                        case RAW_DATA:
                                            log.info("handle raw data resource {} - {}", resourceProvider.getResourceType(), resourceProvider.getSource());
                                            dynamoDbResource.setRawData(resourceProvider.getRawData());
                                            dynamoDbResource.setSource(resourceProvider.getSource());
                                            dynamoDbResource.setHasValue(true);
                                            this.checkAndPutAdditionalResource(addtionalResourceMap, resourceProvider);
                                            return Mono.empty();
                                    }
                                }
                                return Mono.empty();
                            }).then();
                }).then();
    }

    private void checkAndPutAdditionalResource(Map<String, AdditionalResource> addtionalResourceMap, Resource resource) {
        if (StringUtils.isNotBlank(resource.getRawPhoto())) {
            log.info("method: checkandPutAdditionalResource - add photo");
            addtionalResourceMap.put(AssetType.PHOTO_2D.name(), AdditionalResource.builder()
                    .from(resource.getType())
                    .type(AssetType.PHOTO_2D)
                    .hasValue(true)
                    .rawPhoto(resource.getRawPhoto())
                    .resourceType(ResourceType.RAW_BASE64)
                    .source(resource.getSource()).build());
        }
    }

    private String buildResourceKey(String type, String source) {
        return (null == source || BioSource.UNKNOWN.name().equals(source)) ? type :
                type + "-" + source;
    }

    public boolean isResourceExpired(DynamoDbResource resource, long expiredTime) {
        if (0 == expiredTime) {
            return false;
        }
        if (null == resource.getModifiedDate()) {
            return true;
        }
        return new Date(System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(expiredTime)).after(resource.getModifiedDate());
    }

    private Mono<DynamoDbAssetInfo> copyToNewSession(DynamoDbAssetInfo source, List<String> expiredIds) {
        log.info("method: copyToNewSession - pk: {}, sk: {}", source.getPk(), source.getSk());
        Instant now = Instant.now();
        DynamoDbAssetInfo dynamoDbAssetInfo = new DynamoDbAssetInfo();
        dynamoDbAssetInfo.setId(source.getId());
        dynamoDbAssetInfo.setActive(Boolean.TRUE);
        dynamoDbAssetInfo.setCreatedDate(now);
        dynamoDbAssetInfo.setModifiedDate(now);

        dynamoDbAssetInfo.setPk(source.getPk());
        dynamoDbAssetInfo.setSk(Generators.timeBasedEpochGenerator().generate().toString());
        dynamoDbAssetInfo.setIdNoise(MD5Util.hash(dynamoDbAssetInfo.getId()));

        List<Mono<CopyObjectResponse>> copyObjectMonoList = new ArrayList<>();
        List<DynamoDbResource> copiedResource = source.getDynamoDbResources();
        log.info("method: copyToNewSession - copy resource for new session sk: {}", dynamoDbAssetInfo.getSk());
        dynamoDbAssetInfo.setDynamoDbResources(copiedResource.stream().map(r -> {
            if (isPhotoResource(r)) {
                String oldPath = r.getPath();
                String newPath = this.buildS3PhotoPath(dynamoDbAssetInfo.getIdNoise(), dynamoDbAssetInfo.getSk(),
                        FileNameUtils.getFileName(AssetType.valueOf(r.getType()), r.getSource()));
                r.setPath(newPath);
                if (null != r.getId() && expiredIds.contains(r.getId())) {
                    log.info("update expired resource: {} - {} - {}", r.getId(), r.getType(), r.getSource());
                    r.setHasValue(false);
                }
                if (Boolean.TRUE.equals(r.getHasValue())) {
                    copyObjectMonoList.add(s3Connector.copyObject(s3ClientConfig.getBucketName(), s3ClientConfig.getBucketName(), oldPath, newPath));
                }
            }
            r.setCreatedDate(new Date());
            r.setModifiedDate(new Date());
            return r;
        }).collect(Collectors.toList()));

        if (CollectionUtils.isEmpty(copyObjectMonoList)) {
            return Mono.just(dynamoDbAssetInfo);
        }

        return Mono.zip(copyObjectMonoList, tuple -> {
            log.info("copy to new session success");
            return dynamoDbAssetInfo;
        });
    }

    private Mono<GetAssetResponse> buildBioAssetResponse(DynamoDbAssetInfo info,
                                                         List<AssetType> assetTypes,
                                                         Map<String, AdditionalResource> additionalResourceMap,
                                                         List<ProviderDetails> providerDetails) {
        log.info("method: buildBioAssetResponse - start");

        return this.buildResourceResponse(info, assetTypes, additionalResourceMap)
                .collectList()
                .flatMap(resources -> {
                    GetAssetResponse response = GetAssetResponse.builder()
                            .id(info.getId())
                            .session(info.getSk())
                            .resources(resources)
                            .provider(providerDetails)
                            .build();
                    log.info("method: buildBioAssetResponse, response: {}", response);
                    return Mono.just(response);
                });
    }

    @Override
    public Mono<UpdateAssetResponse> updateAsset(UpdateAssetRequest updateAssetRequest) {
        log.info("method: updateBioPhoto - start");

        return this.dynamoDbConnector.getLatestAssetInfo(DynamoDbKey.toDynamoDbKey(updateAssetRequest.getCustomerKey()))
                .switchIfEmpty(Mono.error(new PhotoException("Asset not found")))
                .map(assetInfo -> {
                    List<DynamoDbResource> dynamoDbResources = assetInfo.getDynamoDbResources();

                    updateAssetRequest.getResources()
                            .forEach(e -> {
                                        Optional<DynamoDbResource> dynamoDbResourceOptional = dynamoDbResources.stream()
                                                .filter(info -> info.getId().equals(e.getId()) && info.getPath().equalsIgnoreCase(e.getPath()))
                                                .findFirst();
                                        if (dynamoDbResourceOptional.isPresent()) {
                                            DynamoDbResource dynamoDbResource = dynamoDbResourceOptional.get();
                                            this.toDynamoDbResource(dynamoDbResource, e);
                                        } else {
                                            log.error("Resource not match: {} - {}", e.getType().name(), e.getSource());
                                            throw new PhotoException("Resource not match");
                                        }
                                    }
                            );

                    return assetInfo;
                })
                .flatMap(dynamoDbConnector::update)
                .map(info -> {
                    log.info("method: updateBioPhoto - success");
                    return UpdateAssetResponse.builder()
                            .id(info.getId())
                            .result(Boolean.TRUE)
                            .session(info.getSk())
                            .build();
                })
                .onErrorReturn(UpdateAssetResponse.builder()
                        .id(updateAssetRequest.getCustomerKey().getValue())
                        .result(Boolean.FALSE)
                        .build())
                .doOnSuccess(e -> log.info("method: updateBioPhoto - success"))
                .doOnError(ex -> log.error("method: updateBioPhoto - failed ex: {}", ex.getMessage(), ex));
    }

    @Override
    public Mono<ResetAssetResponse> resetAsset(ResetAssetRequest resetAssetRequest) {
        CustomerKey customerKey = CustomerKey.builder()
                .keyType(resetAssetRequest.getIdType())
                .value(resetAssetRequest.getId())
                .build();

        log.info("method: resetAsset - type: {}, value: {}", resetAssetRequest.getIdType(), resetAssetRequest.getId());

        Mono<ResetBaselineResponse> resetBaselineResponseMono = Mono.just(ResetBaselineResponse.builder().status(false).build());

        if (customerKey.getKeyType() == CustomerKeyType.SA_ID) {
            log.info("resetBaseLine - saId");
            resetBaselineResponseMono = facialVerificationConnector
                    .resetBaseLine(ResetBaselineRequest.builder().said(customerKey.getValue()).build())
                    .onErrorResume(ex -> {
                        log.error(ex.getMessage(), ex);
                        return Mono.just(ResetBaselineResponse.builder().build());
                    });
        }

        if (customerKey.getKeyType() == CustomerKeyType.PROFILE_ID) {
            resetBaselineResponseMono = profileConnector.getMambuProfile(customerKey.getValue()).flatMap(response -> {
                if (!CollectionUtils.isEmpty(response.getIdentities())) {
                    String saId = response.getIdentities().get(0).getSaId();
                    log.info("find saId");
                    return facialVerificationConnector.resetBaseLine(ResetBaselineRequest.builder().said(saId).build())
                            .onErrorReturn(ResetBaselineResponse.builder().build());
                } else {
                    log.error("error find saId: mambu identities null or empty");
                    return Mono.just(ResetBaselineResponse.builder().status(false).build());
                }
            }).onErrorResume(ex -> {
                log.error(ex.getMessage(), ex);
                return Mono.just(ResetBaselineResponse.builder().build());
            });
        }

        resetBaselineResponseMono = resetBaselineResponseMono.switchIfEmpty(Mono.just(ResetBaselineResponse.builder().build()));

        Mono<List<DynamoDbAssetInfo>> dynamoDbAssetInfoMono = this.dynamoDbConnector
                .getAssetInfos(customerKey.toDynamoDbKey())
                .switchIfEmpty(Mono.just(List.of()))
                .flatMap(resources -> Flux.fromIterable(resources).flatMap(resource -> {
                    log.info("DynamoDb delete {}, {}", resource.getPk(), resource.getSk());
                    return this.dynamoDbConnector.delete(resource.getPk(), resource.getSk());
                }).collectList())
                .onErrorReturn(List.of());

        Mono<ResetFacialRecognitionResponse> resetFacialRecognitionResponseMono = this.facialRecognitionConnector
                .resetFacialRecognition(ResetFacialRecognitionRequest.builder().said(customerKey.getValue()).build())
                .onErrorReturn(ResetFacialRecognitionResponse.builder().build());

        resetFacialRecognitionResponseMono = resetFacialRecognitionResponseMono.switchIfEmpty(Mono.just(ResetFacialRecognitionResponse.builder().build()));

        return Mono.zip(resetBaselineResponseMono, dynamoDbAssetInfoMono, resetFacialRecognitionResponseMono).flatMap(response -> {
            ResetBaselineResponse resetBaselineResponse = response.getT1();
            List<DynamoDbAssetInfo> dynamoDbAssetInfo = response.getT2();
            ResetFacialRecognitionResponse resetFacialRecognitionResponse = response.getT3();

            boolean status = resetBaselineResponse.isStatus() && !CollectionUtils.isEmpty(dynamoDbAssetInfo);
            ResetAssetResponse resetAssetResponse = ResetAssetResponse.builder()
                    .bioResources(!CollectionUtils.isEmpty(dynamoDbAssetInfo))
                    .baselines(resetBaselineResponse.getBaselines())
                    .selfieCleanDeduplication(resetFacialRecognitionResponse)
                    .status(status)
                    .build();
            log.info("resetAssetResponse: {}", resetAssetResponse);
            return Mono.just(resetAssetResponse);
        }).onErrorResume(ex -> {
            log.error(ex.getMessage(), ex);
            return Mono.just(ResetAssetResponse.builder().status(false).build());
        });
    }

    private void toDynamoDbResource(DynamoDbResource dynamoDbResource, Resource resource) {
        if (StringUtils.isNotEmpty(resource.getPath())) {
            log.info("Update path: {}", resource.getPath());
            dynamoDbResource.setPath(resource.getPath());
        }
        if (StringUtils.isNotEmpty(resource.getType().toString())) {
            log.info("Update type: {}", resource.getType());
            dynamoDbResource.setType(resource.getType().toString());
        }
        if (StringUtils.isNotEmpty(resource.getSource())) {
            log.info("Update source: {}", resource.getSource());
            dynamoDbResource.setSource(resource.getSource());
        }
        if (StringUtils.isNotEmpty(resource.getVersionId())) {
            log.info("Update versionId: {}", resource.getVersionId());
            dynamoDbResource.setVersionId(resource.getVersionId());
        }
        if (null != resource.getComparisonResult()) {
            log.info("Update comparisonResult: {}", resource.getComparisonResult());
            dynamoDbResource.setComparisonResult(resource.getComparisonResult());
        }

        dynamoDbResource.setHasValue(resource.getHasValue());
        dynamoDbResource.setModifiedDate(null != resource.getModifiedDate() ? resource.getModifiedDate() : new Date());
    }

    private String buildS3PhotoPath(String id, String session, String fileName) {
        return String.format(s3ClientConfig.getPhotoFolderPathFormat(), id, session, fileName);
    }

    private Flux<Resource> buildResourceResponse(DynamoDbAssetInfo dynamoDbAssetInfo, List<AssetType> assetTypes,
                                                 Map<String, AdditionalResource> additionalResourceMap) {
        log.info("method: buildResourceResponse - resource: {}", dynamoDbAssetInfo.getDynamoDbResources());
        if (CollectionUtils.isEmpty(dynamoDbAssetInfo.getDynamoDbResources())) {
            return Flux.empty();
        }

        List<String> types = assetTypes.stream().map(Enum::name).toList();

        return Flux.fromIterable(dynamoDbAssetInfo.getDynamoDbResources())
                .filter(r -> types.contains(r.getType()))
                .flatMap(e -> {
                    Resource resource = new Resource();
                    resource.setId(e.getId());
                    resource.setType(StringUtils.isNotEmpty(e.getType()) ? AssetType.valueOf(e.getType()) : null);
                    resource.setSource(e.getSource());
                    resource.setHasValue(e.getHasValue());
                    resource.setPath(e.getPath());
                    resource.setCreatedDate(e.getCreatedDate());
                    resource.setModifiedDate(e.getModifiedDate());
                    if (isPhotoResource(e) && Boolean.FALSE.equals(e.getHasValue()) && StringUtils.isEmpty(e.getPath())) {
                        resource.setPath(this.buildS3PhotoPath(dynamoDbAssetInfo.getIdNoise(), dynamoDbAssetInfo.getSk(),
                                FileNameUtils.getFileName(resource.getType(), resource.getSource())));
                    }
                    resource.setRawData(e.getRawData());
                    resource.setComparisonResult(e.getComparisonResult());
                    this.includeAdditionalResource(resource, additionalResourceMap, e.getType(), e.getSource());
                    return Mono.just(resource);
                });
    }

    private Mono<GetAssetResponse> buildBioAssetResponseLatest(DynamoDbAssetInfo info,
                                                               Map<String, Resource> idProviderResourceMap,
                                                               List<ProviderDetails> providerDetails) {
        log.info("method: buildBioAssetResponseLatest - start");

        return this.buildLatestResourceFromProvider(info, idProviderResourceMap)
                .collectList()
                .flatMap(resources -> {
                    GetAssetResponse response = GetAssetResponse.builder()
                            .id(info.getId())
                            .session(info.getSk())
                            .resources(resources)
                            .provider(providerDetails)
                            .build();
                    log.info("method: buildBioAssetResponseLatest, response: {}", response);
                    return Mono.just(response);
                });
    }

    private Flux<Resource> buildLatestResourceFromProvider(DynamoDbAssetInfo dynamoDbAssetInfo,
                                                           Map<String, Resource> idProviderResourceMap) {
        log.info("method: buildLatestResourceFromProvider - idProviderResourceMap: {}", idProviderResourceMap);
        if (CollectionUtils.isEmpty(idProviderResourceMap)) {
            return Flux.empty();
        }

        List<Resource> resources = new ArrayList<>();
        for (Map.Entry<String, Resource> entry : idProviderResourceMap.entrySet()) {
            Resource providerResource = entry.getValue();
            Resource resource = new Resource();
            resource.setId(entry.getKey());
            resource.setType(providerResource.getType());
            resource.setSource(providerResource.getSource());
            resource.setHasValue(providerResource.getHasValue());
            resource.setPath(providerResource.getPath());
            resource.setCreatedDate(providerResource.getCreatedDate());
            resource.setModifiedDate(providerResource.getModifiedDate());
            if (AssetType.PHOTO_RESOURCES.contains(providerResource.getType()) && Boolean.FALSE.equals(providerResource.getHasValue()) && StringUtils.isEmpty(providerResource.getPath())) {
                resource.setPath(this.buildS3PhotoPath(dynamoDbAssetInfo.getIdNoise(), dynamoDbAssetInfo.getSk(), FileNameUtils.getFileName(resource.getType(), resource.getSource())));
            }
            resource.setRawData(providerResource.getRawData());
            resource.setRawPhoto(providerResource.getRawPhoto());
            resource.setComparisonResult(providerResource.getComparisonResult());

            resources.add(resource);
        }

        return Flux.fromIterable(resources);
    }

    private void includeAdditionalResource(Resource resource, Map<String, AdditionalResource> additionalResourceMap, String type, String source) {
        for (Map.Entry<String, AdditionalResource> entry : additionalResourceMap.entrySet()) {
            AdditionalResource additionalResource = entry.getValue();
            if (type.equals(additionalResource.getFrom().name()) && source.equalsIgnoreCase(additionalResource.getSource())) {
                if (AssetType.PHOTO_2D.equals(additionalResource.getType())) {
                    resource.setRawPhoto(additionalResource.getRawPhoto());
                }
                break;
            }
        }
    }

    private boolean isPhotoResource(DynamoDbResource dynamoDbResource) {
        return AssetType.PHOTO_RESOURCES.contains(AssetType.valueOf(dynamoDbResource.getType()));
    }

}
