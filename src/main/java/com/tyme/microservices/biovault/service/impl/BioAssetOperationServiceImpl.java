package com.tyme.microservices.biovault.service.impl;

import com.tyme.microservices.biovault.domain.AssetProviderOperationRequest;
import com.tyme.microservices.biovault.domain.AssetProviderOperationResponse;
import com.tyme.microservices.biovault.domain.CustomerKey;
import com.tyme.microservices.biovault.domain.ResetAssetRequest;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbAssetInfo;
import com.tyme.microservices.biovault.domain.enums.CustomerKeyType;
import com.tyme.microservices.biovault.service.BioAssetOperationService;
import com.tyme.microservices.biovault.service.connector.BioAssetProviderConnector;
import com.tyme.microservices.biovault.service.connector.DynamoDbConnector;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Log4j2
@Service
@AllArgsConstructor
public class BioAssetOperationServiceImpl implements BioAssetOperationService {

  private DynamoDbConnector dynamoDbConnector;

  private BioAssetProviderConnector bioAssetProviderConnector;

  @Override
  public Mono<Map<String, Object>> getBioResource(ResetAssetRequest resetAssetRequest) {

    CustomerKey customerKey = CustomerKey.builder()
        .keyType(resetAssetRequest.getIdType())
        .value(resetAssetRequest.getId())
        .build();

    Mono<DynamoDbAssetInfo> dynamoDbAssetInfoMono = this.dynamoDbConnector.getLatestAssetInfo(
            customerKey.toDynamoDbKey())
        .switchIfEmpty(Mono.just(DynamoDbAssetInfo.builder().build()));
    Mono<AssetProviderOperationResponse> operationAssetFromProvider;
    if (customerKey.getKeyType() == CustomerKeyType.PROFILE_ID) {
      log.info("getOperationAssetFromProvider");
      operationAssetFromProvider = bioAssetProviderConnector.getOperationAssetFromProvider(
          AssetProviderOperationRequest.builder()
              .profileId(customerKey.getValue())
              .build());
    } else {
      operationAssetFromProvider = Mono.just(AssetProviderOperationResponse.builder().build());
    }

    return Mono.zip(dynamoDbAssetInfoMono, operationAssetFromProvider)
        .map(result -> Map.of("BioAssetInfo", result.getT1(), "BioAssetProviderInfo",
            result.getT2()));
  }
}
