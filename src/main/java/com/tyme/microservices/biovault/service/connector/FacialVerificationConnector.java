package com.tyme.microservices.biovault.service.connector;

import com.tyme.microservices.biovault.domain.ResetBaselineRequest;
import com.tyme.microservices.biovault.domain.ResetBaselineResponse;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Component
@Log4j2
public class FacialVerificationConnector {

    private final WebClient facialVerificationWebClient;

    public FacialVerificationConnector(WebClient facialVerificationWebClient) {
        this.facialVerificationWebClient = facialVerificationWebClient;
    }

    public Mono<ResetBaselineResponse> resetBaseLine(ResetBaselineRequest resetBaselineRequest) {
        log.info("method: resetBaseLine - start");

        return facialVerificationWebClient.post()
                .uri(uriBuilder -> uriBuilder
                        .path("/facial-verification-operation/reset")
                        .build())
                .bodyValue(resetBaselineRequest)
                .retrieve()
                .bodyToMono(ResetBaselineResponse.class)
                .onErrorResume(Mono::error);
    }
}
