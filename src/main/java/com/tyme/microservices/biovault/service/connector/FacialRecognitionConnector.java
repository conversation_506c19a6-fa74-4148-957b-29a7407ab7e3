package com.tyme.microservices.biovault.service.connector;

import com.tyme.microservices.biovault.domain.ResetFacialRecognitionRequest;
import com.tyme.microservices.biovault.domain.ResetFacialRecognitionResponse;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Component
@Log4j2
public class FacialRecognitionConnector {

    private final WebClient facialRecognitionWebClient;

    public FacialRecognitionConnector(WebClient facialRecognitionWebClient) {
        this.facialRecognitionWebClient = facialRecognitionWebClient;
    }

    public Mono<ResetFacialRecognitionResponse> resetFacialRecognition(ResetFacialRecognitionRequest resetFacialRecognitionRequest) {
        log.info("method: resetFacialRecognition - start");

        return facialRecognitionWebClient.post()
                .uri(uriBuilder -> uriBuilder
                        .path("/facial-recognition-operation/reset")
                        .build())
                .bodyValue(resetFacialRecognitionRequest)
                .retrieve()
                .bodyToMono(ResetFacialRecognitionResponse.class)
                .onErrorResume(Mono::error);
    }
}
