package com.tyme.microservices.biovault.service.impl;

import com.tyme.microservices.biovault.domain.AssetProviderBioSource;
import com.tyme.microservices.biovault.domain.AssetProviderFallbackBioSource;
import com.tyme.microservices.biovault.domain.AssetProviderRequestV2;
import com.tyme.microservices.biovault.domain.AssetProviderResponse;
import com.tyme.microservices.biovault.domain.AssetProviderResponseV2;
import com.tyme.microservices.biovault.domain.CustomerKey;
import com.tyme.microservices.biovault.domain.ProviderDetails;
import com.tyme.microservices.biovault.domain.Resource;
import com.tyme.microservices.biovault.domain.enums.AssetProviderRequestDataType;
import com.tyme.microservices.biovault.domain.enums.AssetProviderSourceType;
import com.tyme.microservices.biovault.service.BAPIntegrationService;
import com.tyme.microservices.biovault.service.BioAssetProviderService;
import com.tyme.microservices.biovault.service.connector.BioAssetProviderConnector;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;


@Service
@Log4j2
@RequiredArgsConstructor
public class BioAssetProviderServiceImpl implements BioAssetProviderService {

    private final BioAssetProviderConnector providerConnector;

    private final BAPIntegrationService bapIntegrationService;

    @Override
    public Mono<AssetProviderResponse> getFaceMapFromProvider(CustomerKey customerKey) {
        AssetProviderRequestV2 assetProviderRequestV2 = AssetProviderRequestV2.builder()
                .customerKey(customerKey)
                .sources(List.of(
                        AssetProviderBioSource.builder()
                                .source(AssetProviderSourceType.FACETEC_COMPARISON)
                                .build()
                ))
                .build();
        return providerConnector.getAssetFromProviderV2(assetProviderRequestV2, AssetProviderRequestDataType.DATA)
                .flatMap(response -> Mono.just(this.toResources(response)));
    }

    @Override
    public Mono<AssetProviderResponse> getFaceMapFallbackPhoto2DFromProvider(CustomerKey customerKey) {
        AssetProviderRequestV2 assetProviderRequestV2 = bapIntegrationService.buildFaceMapFallbackRequest(customerKey);
        return providerConnector.getAssetFromProviderV2(assetProviderRequestV2, AssetProviderRequestDataType.DATA)
                .flatMap(response -> Mono.just(this.toResources(response)));
    }

    private AssetProviderResponse toResources(List<AssetProviderResponseV2> responses) {
        List<Resource> resources = new ArrayList<>();
        List<ProviderDetails> providers = new ArrayList<>();

        for (var response : responses) {
            if (!CollectionUtils.isEmpty(response.getResources())) {
                resources.addAll(response.getResources());
            }

            if (response.getProviderDetails() != null) {
                providers.add(response.getProviderDetails());
            }
            fallbackSources(response, resources, providers);
        }
        return AssetProviderResponse.builder().resources(resources).provider(providers).build();
    }

    private void fallbackSources(AssetProviderResponseV2 response, List<Resource> resources, List<ProviderDetails> providers) {
        if (!CollectionUtils.isEmpty(response.getFallbackSources())) {
            for (var item : response.getFallbackSources()) {
                if (!CollectionUtils.isEmpty(item.getResources())) {
                    resources.addAll(item.getResources());
                }

                if (item.getProviderDetails() != null) {
                    providers.add(item.getProviderDetails());
                }
            }
        }
    }

}
