package com.tyme.microservices.biovault.service.impl;

import com.tyme.microservices.biovault.domain.AssetProviderBioSource;
import com.tyme.microservices.biovault.domain.AssetProviderFallbackBioSource;
import com.tyme.microservices.biovault.domain.AssetProviderRequestV2;
import com.tyme.microservices.biovault.domain.CustomerKey;
import com.tyme.microservices.biovault.domain.enums.AssetProviderSourceType;
import com.tyme.microservices.biovault.service.BAPIntegrationService;
import java.util.List;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

@Log4j2
@Service
@Profile("sa")
public class BAPIntegrationSAServiceImpl implements BAPIntegrationService {

  @Override
  public AssetProviderRequestV2 buildFaceMapFallbackRequest(CustomerKey customerKey) {
    return AssetProviderRequestV2.builder()
        .customerKey(customerKey)
        .sources(List.of(
            AssetProviderBioSource.builder()
                .source(AssetProviderSourceType.FACETEC_COMPARISON)
                .fallbackBioSources(List.of(
                    AssetProviderFallbackBioSource.builder()
                        .source(AssetProviderSourceType.HANIS)
                        .ordinal(1)
                        .build(),
                    AssetProviderFallbackBioSource.builder()
                        .source(AssetProviderSourceType.CPB)
                        .ordinal(2)
                        .build()
                ))
                .build()
        ))
        .build();
  }
}
