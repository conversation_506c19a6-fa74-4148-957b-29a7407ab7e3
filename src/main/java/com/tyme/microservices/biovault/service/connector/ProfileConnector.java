package com.tyme.microservices.biovault.service.connector;

import com.tyme.microservices.biovault.domain.MambuProfile;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Component
@Log4j2
public class ProfileConnector {

    private final WebClient profileWebClient;

    public ProfileConnector(WebClient profileWebClient) {
        this.profileWebClient = profileWebClient;
    }

    public Mono<MambuProfile> getMambuProfile(String profileId) {
        return profileWebClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/profile/{profileId}")
                        .queryParam("detailsLevel", "FULL")
                        .build(profileId))
                .exchangeToMono(response -> {
                    if (response.statusCode().equals(HttpStatus.OK)) {
                        return response.bodyToMono(MambuProfile.class);
                    } else {
                        return response.createException()
                                .flatMap(Mono::error);
                    }
                });
    }
}
