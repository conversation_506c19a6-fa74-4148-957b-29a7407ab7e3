package com.tyme.microservices.biovault.service.connector;

import com.tyme.microservices.biovault.domain.AssetProviderOperationRequest;
import com.tyme.microservices.biovault.domain.AssetProviderOperationResponse;
import com.tyme.microservices.biovault.domain.AssetProviderRequest;
import com.tyme.microservices.biovault.domain.AssetProviderRequestV2;
import com.tyme.microservices.biovault.domain.AssetProviderResponse;
import com.tyme.microservices.biovault.domain.AssetProviderResponseV2;
import com.tyme.microservices.biovault.domain.enums.AssetProviderRequestDataType;
import java.util.List;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Component
@Log4j2
public class BioAssetProviderConnector {

    private final WebClient bapWebClient;

    @Value("${app.config.infra.bap.customer-assets-path}")
    private String customerAssetsPath;

    public BioAssetProviderConnector(WebClient bapWebClient) {
        this.bapWebClient = bapWebClient;
    }

    public Mono<AssetProviderResponse> getAssetFromProvider(AssetProviderRequest assetProviderRequest) {
        log.info("method: getAssetFromProvider - start");

        return bapWebClient.post()
                .uri(uriBuilder -> uriBuilder
                        .path("/bap/assets")
                        .build())
                .bodyValue(assetProviderRequest)
                .retrieve()
                .bodyToMono(AssetProviderResponse.class)
                .onErrorResume(Mono::error);
    }


    public Mono<AssetProviderOperationResponse> getOperationAssetFromProvider(AssetProviderOperationRequest assetProviderOperationRequest) {
        log.info("method: getOperationAssetFromProvider - start");
        return bapWebClient.post()
            .uri(uriBuilder -> uriBuilder
                .path("/internal/bap-operation/get-3d-enrollment")
                .build())
            .bodyValue(assetProviderOperationRequest)
            .retrieve()
            .bodyToMono(AssetProviderOperationResponse.class)
            .onErrorResume(Mono::error);
    }

    public Mono<List<AssetProviderResponseV2>> getAssetFromProviderV2(AssetProviderRequestV2 assetProviderRequest, AssetProviderRequestDataType dataType) {
        log.info("method: getAssetFromProviderV2 - start");

        return bapWebClient.post()
                .uri(uriBuilder -> uriBuilder
                        .path(customerAssetsPath)
                        .queryParam("type", dataType)
                        .build())
                .bodyValue(assetProviderRequest)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<List<AssetProviderResponseV2>>() {})
                .onErrorResume(Mono::error);
    }
}
