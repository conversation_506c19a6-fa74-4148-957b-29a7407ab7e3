package com.tyme.microservices.biovault.service.connector;

import com.tyme.microservices.biovault.config.DynamoDbConfig;
import com.tyme.microservices.biovault.domain.dynamodb.DynamoDbAssetInfo;
import java.util.List;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbAsyncTable;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient;
import software.amazon.awssdk.enhanced.dynamodb.Expression;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.DeleteItemEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.PutItemEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.UpdateItemEnhancedRequest;
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient;

import java.util.Objects;
import java.util.Optional;

@Component
@Log4j2
public class DynamoDbConnector {

    private final DynamoDbConfig dynamoDbConfig;

    private final DynamoDbAsyncClient dynamoDbAsyncClient;

    private final DynamoDbEnhancedAsyncClient enhancedAsyncClient;

    private DynamoDbAsyncTable<DynamoDbAssetInfo> assetInfoDynamoDbAsyncTable;

    private final Expression pkAndSkExistsExpression = Expression.builder()
            .expression("attribute_exists(pk) and attribute_exists(sk)")
            .build();

    public DynamoDbConnector(DynamoDbConfig dynamoDbConfig, DynamoDbAsyncClient dynamoDbAsyncClient,
                             DynamoDbEnhancedAsyncClient enhancedAsyncClient) {
        this.dynamoDbConfig = dynamoDbConfig;
        this.dynamoDbAsyncClient = dynamoDbAsyncClient;
        this.enhancedAsyncClient = enhancedAsyncClient;
        this.assetInfoDynamoDbAsyncTable = this.enhancedAsyncClient.table(this.dynamoDbConfig.getAssetInfoTableName(), TableSchema.fromClass(DynamoDbAssetInfo.class));
        createDynamoDbTables();
    }

    private void createDynamoDbTables() {
        if (dynamoDbConfig.isProfileActive(dynamoDbConfig.getLocalEnv()) || dynamoDbConfig.isProfileActive(dynamoDbConfig.getTestEnv())) {
            dynamoDbAsyncClient.listTables().thenAccept(listTablesResponse -> {
                Optional<String> optional = listTablesResponse.tableNames().stream().filter(table -> table.equals(dynamoDbConfig.getAssetInfoTableName())).findFirst();
                if (optional.isEmpty()) {
                    assetInfoDynamoDbAsyncTable.createTable().join();
                    log.info("createTableResponse: {}", dynamoDbConfig.getAssetInfoTableName());
                } else {
                    log.info("Table {} already exists", dynamoDbConfig.getAssetInfoTableName());
                }
            }).join();

        }
    }

    public Mono<Void> save(DynamoDbAssetInfo assetInfo) {
        return Mono.fromFuture(this.assetInfoDynamoDbAsyncTable.putItem(PutItemEnhancedRequest
                        .builder(DynamoDbAssetInfo.class)
                        .item(assetInfo)
                        .build()))
                .doOnSuccess(e -> log.info("method: save - success"))
                .doOnError(ex -> log.error("method: save - failed, ex: {}", ex.getMessage(), ex));
    }

    public Mono<DynamoDbAssetInfo> update(DynamoDbAssetInfo assetInfo) {
        return Mono.fromFuture(this.assetInfoDynamoDbAsyncTable.updateItem(UpdateItemEnhancedRequest
                        .builder(DynamoDbAssetInfo.class)
                        .item(assetInfo)
                        .conditionExpression(pkAndSkExistsExpression)
                        .ignoreNulls(true)
                        .build()))
                .doOnSuccess(e -> log.info("method: update - success"))
                .doOnError(ex -> log.error("method: update - failed, ex: {}", ex.getMessage(), ex));

    }

    public Mono<DynamoDbAssetInfo> delete(String pk, String sk) {
        DeleteItemEnhancedRequest deleteItemEnhancedRequest = DeleteItemEnhancedRequest.builder()
                .key(Key.builder().partitionValue(pk).sortValue(sk).build()).build();
        return Mono.fromFuture(this.assetInfoDynamoDbAsyncTable.deleteItem(deleteItemEnhancedRequest)
                )
                .doOnSuccess(e -> log.info("method: delete - success"))
                .doOnError(ex -> log.error("method: delete - failed, ex: {}", ex.getMessage(), ex));

    }

    public Mono<List<DynamoDbAssetInfo>> getAssetInfos(String pk) {
        log.info("method: getAssetInfos from DynamoDb, pk: {}", pk);

        return Mono.from(assetInfoDynamoDbAsyncTable
                        .query(QueryEnhancedRequest.builder()
                                .queryConditional(QueryConditional
                                        .keyEqualTo(Key.builder()
                                                .partitionValue(pk)
                                                .build()))
                                .scanIndexForward(false)
                                .build())
                        .map(page -> CollectionUtils.isEmpty(page.items()) ? null : page.items()))
                .filter(Objects::nonNull);
    }

    public Mono<DynamoDbAssetInfo> getLatestAssetInfo(String pk) {
        log.info("method: getLatestAssetInfo from DynamoDb, pk: {}", pk);

        return Mono.from(assetInfoDynamoDbAsyncTable
                .query(QueryEnhancedRequest.builder()
                        .queryConditional(QueryConditional
                                .keyEqualTo(Key.builder()
                                        .partitionValue(pk)
                                        .build()))
                        .limit(1)
                        .scanIndexForward(false)
                        .build())
                .map(page -> CollectionUtils.isEmpty(page.items()) ? null : page.items().get(0)))
                .filter(Objects::nonNull);
    }
}
