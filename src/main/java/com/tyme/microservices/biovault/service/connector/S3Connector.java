package com.tyme.microservices.biovault.service.connector;

import com.tyme.microservices.biovault.config.S3ClientConfig;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.core.async.AsyncRequestBody;
import software.amazon.awssdk.core.async.AsyncResponseTransformer;
import software.amazon.awssdk.services.s3.S3AsyncClient;
import software.amazon.awssdk.services.s3.model.Bucket;
import software.amazon.awssdk.services.s3.model.CopyObjectRequest;
import software.amazon.awssdk.services.s3.model.CopyObjectResponse;
import software.amazon.awssdk.services.s3.model.CreateBucketRequest;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.HeadObjectRequest;
import software.amazon.awssdk.services.s3.model.HeadObjectResponse;
import software.amazon.awssdk.services.s3.model.MetadataDirective;
import software.amazon.awssdk.services.s3.model.ObjectCannedACL;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Component
@RequiredArgsConstructor
@Log4j2
public class S3Connector {

    private final S3ClientConfig s3ClientConfig;

    private final S3AsyncClient s3AsyncClient;

    @PostConstruct
    public void createBucket() {
        if (s3ClientConfig.isProfileActive(s3ClientConfig.getLocalEnv())
                || s3ClientConfig.isProfileActive(s3ClientConfig.getTestEnv())) {
            log.info("createBucket");
            s3AsyncClient.listBuckets().thenAccept(listBucketsResponse -> {
                List<String> buckets = List.of(s3ClientConfig.getBucketName(), s3ClientConfig.getBaselineBucketName());
                for (String bucket : buckets) {
                    Optional<Bucket> optionalBucket = listBucketsResponse.buckets().stream()
                            .filter(e -> e.name().equalsIgnoreCase(bucket)).findFirst();
                    if (optionalBucket.isEmpty()) {
                        CreateBucketRequest createBucketRequest = CreateBucketRequest.builder()
                                .bucket(bucket)
                                .build();

                        s3AsyncClient.createBucket(createBucketRequest)
                                .thenAccept(createBucketResponse -> log.info("Create bucket: {}", createBucketResponse.toString()))
                                .join();
                    }
                }
            }).join();
        }
    }

    public Mono<PutObjectResponse> putObject(final String bucket, final String path, final byte[] content) {
        log.info("method: putObject");

        PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                .bucket(bucket)
                .acl(ObjectCannedACL.BUCKET_OWNER_FULL_CONTROL)
                .key(path)
                .contentLength((long) content.length)
                .contentType(MediaType.APPLICATION_OCTET_STREAM.toString())
                .build();
        CompletableFuture<PutObjectResponse> putObjectResponseCompletableFuture = s3AsyncClient.putObject(putObjectRequest,
                AsyncRequestBody.fromBytes(content));

        return Mono.fromFuture(putObjectResponseCompletableFuture)
                .doOnSuccess(e -> log.info("method: putObject - success"))
                .doOnError(ex -> log.error("method: putObject - failed, ex: {}", ex.getMessage(), ex));
    }

    public Mono<byte[]> getObject(final String bucket, final String path) {
        log.info("method: getObject");

        GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                .bucket(bucket)
                .key(path)
                .build();

        return Mono.fromFuture(s3AsyncClient.getObject(getObjectRequest, AsyncResponseTransformer.toBytes())
                        .thenApply(ResponseBytes::asByteArray))
                .doOnSuccess(e -> log.info("method: getObject - success"))
                .doOnError(ex -> log.error("method: getObject - failed, ex: {}", ex.getMessage(), ex));
    }

    public Mono<HeadObjectResponse> isFileExisted(final String bucket, final String path) {
        HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                .bucket(bucket)
                .key(path)
                .build();
        CompletableFuture<HeadObjectResponse> responseCompletableFuture = s3AsyncClient.headObject(headObjectRequest);
        return Mono.fromFuture(responseCompletableFuture);
    }

    public Mono<CopyObjectResponse> copyObject(String sourceBucket, String targetBucket,
                                               String sourceLocation, String targetLocation) {
        var copyObjectRequest = CopyObjectRequest.builder()
                .sourceBucket(sourceBucket)
                .sourceKey(sourceLocation)
                .destinationBucket(targetBucket)
                .destinationKey(targetLocation)
                .metadataDirective(MetadataDirective.COPY)
                .acl(ObjectCannedACL.BUCKET_OWNER_FULL_CONTROL)
                .build();
        log.info("method: copyObject source: {} - target: {}", sourceLocation, targetLocation);
        return Mono.fromFuture(s3AsyncClient.copyObject(copyObjectRequest));
    }
}
