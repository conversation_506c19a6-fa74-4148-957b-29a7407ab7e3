package com.tyme.microservices.biovault.service;

import com.tyme.microservices.biovault.domain.GetAssetRequest;
import com.tyme.microservices.biovault.domain.GetAssetResponse;
import com.tyme.microservices.biovault.domain.ResetAssetRequest;
import com.tyme.microservices.biovault.domain.ResetAssetResponse;
import com.tyme.microservices.biovault.domain.UpdateAssetRequest;
import com.tyme.microservices.biovault.domain.UpdateAssetResponse;
import reactor.core.publisher.Mono;

public interface BioAssetService {

    Mono<GetAssetResponse> searchAsset(GetAssetRequest getAssetRequest);

    Mono<UpdateAssetResponse> updateAsset(UpdateAssetRequest updateAssetRequest);

    Mono<ResetAssetResponse> resetAsset(ResetAssetRequest resetAssetRequest);
}
