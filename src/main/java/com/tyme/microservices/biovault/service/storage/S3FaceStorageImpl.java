package com.tyme.microservices.biovault.service.storage;

import com.tyme.microservices.biovault.config.S3ClientConfig;
import com.tyme.microservices.biovault.service.connector.S3Connector;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;

@Service
@Log4j2
public class S3FaceStorageImpl implements S3FaceStorage {

    private final S3Connector s3Connector;

    private final S3ClientConfig s3ClientConfig;

    @Autowired
    public S3FaceStorageImpl(S3Connector s3Connector, S3ClientConfig s3ClientConfig) {
        this.s3Connector = s3Connector;
        this.s3ClientConfig = s3ClientConfig;
    }

    @Override
    public Mono<PutObjectResponse> uploadPhoto(String path, byte[] content) {
        return this.s3Connector.putObject(s3ClientConfig.getBucketName(), path, content);
    }
}
