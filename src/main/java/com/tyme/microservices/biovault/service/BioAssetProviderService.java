package com.tyme.microservices.biovault.service;

import com.tyme.microservices.biovault.domain.AssetProviderResponse;
import com.tyme.microservices.biovault.domain.AssetProviderResponseV2;
import com.tyme.microservices.biovault.domain.CustomerKey;
import com.tyme.microservices.biovault.domain.Resource;
import java.util.List;
import reactor.core.publisher.Mono;

public interface BioAssetProviderService {

    Mono<AssetProviderResponse> getFaceMapFromProvider(CustomerKey customerKey);
    Mono<AssetProviderResponse> getFaceMapFallbackPhoto2DFromProvider(CustomerKey customerKey);

}
