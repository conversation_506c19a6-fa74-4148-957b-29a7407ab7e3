Parameters:
  FacialCentralBucketName:
    Type: String
  DefaultRegion:
    Type: String
  BioAssetProviderUrl:
    Type: AWS::SSM::Parameter::Value<String>
    Default: /tp/authentication/bio-asset-provider-svc/url
  FacialVerificationUrl:
    Type: AWS::SSM::Parameter::Value<String>
    Default: /tp/authentication/facial-verification-svc/url
  ProfileUrl:
    Type: AWS::SSM::Parameter::Value<String>
    Default: /tp/authentication/profile-svc/url
  FacialRecognitionUrl:
    Type: AWS::SSM::Parameter::Value<String>
    Default: /tp/authentication/facial-recognition-svc/url
  ProductName:
    Type: String
    Default: 'bio-vault-svc'
  Namespace:
    Type: String
    Default: 'tyme/services'
  CustomerAssetsPath:
    Type: String
    Default: '/internal/bap/assets'
  ActiveCountry:
    Type: String
Resources:
  TaskDefinition:
    Properties:
      ContainerDefinitions:
        - Name: !Sub ${ResourceName}
          Environment:
            - Name: DEFAULT_REGION
              Value: !Ref DefaultRegion
            - Name: FACIAL_CENTRAL_S3_BUCKET_NAME
              Value: !Ref FacialCentralBucketName
            - Name: BIO_ASSET_PROVIDER_URL
              Value: !Ref BioAssetProviderUrl
            - Name: FACIAL_VERIFICATION_URL
              Value: !Ref FacialVerificationUrl
            - Name: PROFILE_URL
              Value: !Ref ProfileUrl
            - Name: FACIAL_RECOGNITION_URL
              Value: !Ref FacialRecognitionUrl
            - Name: CUSTOMER_ASSETS_PATH
              Value: !Ref CustomerAssetsPath
            - Name: ACTIVE_COUNTRY
              Value: !Ref ActiveCountry