{"Parameters": {"ServicesSecurityGroupId": "/tp/customer-vault/customer-vault-cluster/services-security-group/group-id", "DefaultCapacityProvider": "FARGATE_SPOT", "ApiPort": "8138", "TaskCPU": "2048", "TaskMemory": "4096", "DefaultRegion": "ap-southeast-1", "FacialCentralBucketName": "facial-central.tst.sg.gotyme.com.ph", "UseFireLens": "Yes", "UseDataDog": "Yes", "FluentBitVersion": "1.0.4", "FluentBitMaskingRegrexs": "[  {    \"message_pattern\": \"(%d%d%d%d)(%d%d%d%d)(%d%d%d%d)(%d)\",    \"replace_pattern\": \"%1****%3%4\"  },  {    \"message_pattern\": \"(%d%d%d%d)(%d%d%d%d)(%d%d)\",    \"replace_pattern\": \"%1****%3\"  },  {    \"message_pattern\": \"profileId=(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w)\",    \"replace_pattern\": \"profileId=%1%2***************%6%7\"  },  {    \"message_pattern\": \"profileId: (%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w)\",    \"replace_pattern\": \"profileId: %1%2***************%6%7\"  },  {    \"message_pattern\": \"profileId#(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w)\",    \"replace_pattern\": \"profileId#%1%2***************%6%7\"  },  {    \"message_pattern\": \"value=(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w)\",    \"replace_pattern\": \"value=%1%2***************%6%7\"  },  {    \"message_pattern\": \"id=(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w)\",    \"replace_pattern\": \"id=%1%2***************%6%7\"  }]", "ActiveCountry": "ph"}, "Tags": {"tyme:business-unit": "origination-customer", "tyme:app-name": "channel-services", "tyme:app-function": "bio-vault-service", "tyme:environment": "tst", "tyme:requestor": "<EMAIL>", "tyme:entity": "gt", "tyme:value-stream": "customer"}}