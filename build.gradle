plugins {
	id 'java'
	id 'org.springframework.boot' version '3.3.1'
	id 'io.spring.dependency-management' version '1.1.5'
	id 'org.graalvm.buildtools.native' version '0.10.2'
	id 'org.asciidoctor.jvm.convert' version '3.3.2'
	id 'jacoco'
	id 'org.sonarqube' version '4.4.1.3373'
}

group = 'com.tyme.microservices'
version = '0.0.1-SNAPSHOT'

java {
	sourceCompatibility = '17'
}

configurations {
	all*.exclude module: 'spring-boot-starter-logging'
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	mavenCentral()
	mavenLocal()
	maven {
		url 'https://plugins.gradle.org/m2/'
	}

	maven {
		url "https://${System.env.CODEARTIFACT_DOMAIN}-${System.env.AWS_ACCOUNT_ID}.d.codeartifact.${System.env.AWS_REGION}.amazonaws.com/maven/libs-release/"
		credentials {
			username 'aws'
			password System.env.CODEARTIFACT_AUTH_TOKEN
		}
	}

	// Below code is used for fetching SNAPSHOT artifacts only, SNAPSHOT should not exist in UAT and PROD env
	maven {
		url "https://${System.env.CODEARTIFACT_DOMAIN}-${System.env.AWS_ACCOUNT_ID}.d.codeartifact.${System.env.AWS_REGION}.amazonaws.com/maven/libs-snapshot/"
		credentials {
			username 'aws'
			password System.env.CODEARTIFACT_AUTH_TOKEN
		}
	}
}

jar {
	enabled = false
}

ext {
	set('snippetsDir', file('build/generated-snippets'))
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation ('org.springframework.boot:spring-boot-starter-webflux') {
		exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
	}
	implementation 'org.springframework.boot:spring-boot-starter-log4j2'

	//SpringCloud
	implementation platform("io.awspring.cloud:spring-cloud-aws-dependencies:${springCloudAwsVersion}")
	implementation 'io.awspring.cloud:spring-cloud-aws-starter-dynamodb'
	implementation 'io.awspring.cloud:spring-cloud-aws-starter-sqs'

	implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:${springdocVersion}"

	//LOMBOK
	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'
	testAnnotationProcessor "org.projectlombok:lombok"
	testImplementation 'org.projectlombok:lombok:1.18.28'

	//COMMONS LIB
	implementation 'org.apache.commons:commons-lang3:3.14.0'
	implementation 'com.fasterxml.uuid:java-uuid-generator:4.3.0'
	implementation 'commons-codec:commons-codec:1.16.1'


	//AWS SDK
	implementation platform("software.amazon.awssdk:bom:${awsSdkVersion}")
	implementation 'software.amazon.awssdk:aws-core'
	implementation 'software.amazon.awssdk:s3'
	implementation 'software.amazon.awssdk:dynamodb'

	//DATABASE

	// TESTING
	testImplementation ('org.springframework.boot:spring-boot-starter-test') {
		exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
	}
	testImplementation 'io.projectreactor:reactor-test'
	testImplementation 'org.springframework.restdocs:spring-restdocs-webtestclient'
	testImplementation 'org.springframework.boot:spring-boot-testcontainers'
	testImplementation 'org.testcontainers:testcontainers'
	testImplementation 'org.testcontainers:localstack'
	testImplementation 'org.mockito:mockito-core:5.9.0'
	testImplementation 'org.mockito:mockito-junit-jupiter:5.9.0'
	testImplementation 'com.squareup.okhttp3:mockwebserver:4.12.0'

}

sonarqube {
	properties {
		property 'sonar.organization', 'tymerepos'
		property 'sonar.host.url', 'https://sonarcloud.io'
		property 'sonar.qualitygate.wait', 'true'
	}
}

if (hasProperty('buildScan')) {
	buildScan {
		termsOfServiceUrl = 'https://gradle.com/terms-of-service'
		termsOfServiceAgree = 'no'
	}
}

jacocoTestReport {
	reports {
		xml.required.set(true)
	}
}

tasks['test'].finalizedBy jacocoTestReport
tasks['sonarqube'].dependsOn jacocoTestReport

tasks.named('test') {
	outputs.dir snippetsDir
	useJUnitPlatform()
}

tasks.named('asciidoctor') {
	inputs.dir snippetsDir
	dependsOn test
}
