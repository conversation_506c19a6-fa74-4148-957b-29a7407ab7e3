echo starting app...

if [ "true" == "$DD_ENABLE" ]; then
    DD_FILE=/dd-java-agent.jar
    if [ ! -z $DD_TRACER_VERSION ]; then
        echo "Download $DD_TRACER_VERSION version."
        wget -q -O $DD_FILE "https://repo1.maven.org/maven2/com/datadoghq/dd-java-agent/${DD_TRACER_VERSION}/dd-java-agent-${DD_TRACER_VERSION}.jar"
    else
        echo "Missing Datadog Tracer Version Configuration. Download latest version."
        wget -q -O $DD_FILE https://dtdg.co/latest-java-tracer
    fi
    JAVA_OPTS="$JAVA_OPTS -javaagent:$DD_FILE -Ddd.aws.propagation.enabled=true -Ddd.kafka.legacy.tracing.enabled=false -Ddd.aws.propagation.enabled=true -Dcom.sun.management.jmxremote"
    JAVA_OPTS="$JAVA_OPTS -Ddd.service.mapping=sqs:${DD_SERVICE},java-aws-sdk:${DD_SERVICE}"
fi

JAVA_OPTS="$JAVA_OPTS -Djava.security.egd=file:/dev/./urandom"
JAVA_OPTS="$JAVA_OPTS -Dlogging.path=$LOG_DIR"
JAVA_OPTS="$JAVA_OPTS -XX:MaxRAMPercentage=80.0"

exec java $JAVA_OPTS org.springframework.boot.loader.launch.JarLauncher