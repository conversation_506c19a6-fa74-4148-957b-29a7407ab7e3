FROM 672490406318.dkr.ecr.ap-southeast-1.amazonaws.com/amazoncorretto:17-rc2 AS builder

COPY build/libs/*.jar app.jar
RUN java -Djarmode=layertools -jar app.jar extract

FROM 672490406318.dkr.ecr.ap-southeast-1.amazonaws.com/amazoncorretto:17-rc2

COPY --from=builder dependencies/ ./
COPY --from=builder snapshot-dependencies/ ./
RUN true
COPY --from=builder spring-boot-loader/ ./
COPY --from=builder application/ ./

EXPOSE 8138

ENV LOG_DIR="/logs"

RUN sh -c 'touch /org.springframework.boot.loader.launch.JarLauncher.jar' \
  && mkdir $LOG_DIR \
  && chown -R 1000:1000 $LOG_DIR \
  && chown -R 1000:1000 org.springframework.boot.loader.launch.JarLauncher.jar

VOLUME ["$LOG_DIR"]

ADD ./entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

USER $TYME_USER

ENTRYPOINT /entrypoint.sh