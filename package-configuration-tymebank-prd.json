{"Parameters": {"ServicesSecurityGroupId": "/tp/customer-vault/customer-vault-cluster/services-security-group/group-id", "DefaultCapacityProvider": "FARGATE", "ApiPort": "8138", "TaskCPU": "2048", "TaskMemory": "4096", "DefaultRegion": "eu-west-1", "FacialCentralBucketName": "facial-central.prd.tymebank.co.za", "UseFireLens": "Yes", "UseDataDog": "Yes", "FluentBitVersion": "1.0.4", "FluentBitMaskingRegrexs": "[  {    \"message_pattern\": \"(%d%d%d%d)(%d%d%d%d)(%d%d%d%d)(%d)\",    \"replace_pattern\": \"%1****%3%4\"  },  {    \"message_pattern\": \"(%d%d%d%d)(%d%d%d%d)(%d%d)\",    \"replace_pattern\": \"%1****%3\"  },  {    \"message_pattern\": \"profileId=(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w)\",    \"replace_pattern\": \"profileId=%1%2***************%6%7\"  },  {    \"message_pattern\": \"profileId: (%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w)\",    \"replace_pattern\": \"profileId: %1%2***************%6%7\"  },  {    \"message_pattern\": \"profileId#(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w)\",    \"replace_pattern\": \"profileId#%1%2***************%6%7\"  },  {    \"message_pattern\": \"value=(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w)\",    \"replace_pattern\": \"value=%1%2***************%6%7\"  },  {    \"message_pattern\": \"id=(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w)\",    \"replace_pattern\": \"id=%1%2***************%6%7\"  }]", "CustomerAssetsPath": "/v2/bap/assets", "ActiveCountry": "sa"}, "Tags": {"tyme:business-unit": "origination-customer", "tyme:app-name": "channel-services", "tyme:app-function": "bio-vault-service", "tyme:environment": "prd", "tyme:requestor": "<EMAIL>", "tyme:entity": "tymebank", "tyme:business-priority": "other", "Tier": "prd", "Scheduler": "N/A", "tyme:value-stream": "customer"}}